/* Enterprise Hero Section - Advanced CSS */

/* Hero Container */
.hero-enterprise {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  perspective: 1000px;
}

/* Dynamic Canvas Background */
.hero-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

/* Gradient Overlay */
.hero-gradient-overlay {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(230, 0, 0, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.3) 0%, transparent 50%),
    linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(230, 0, 0, 0.4) 50%, rgba(255, 215, 0, 0.6) 100%);
  z-index: 2;
  animation: gradientFlow 20s ease-in-out infinite;
}

@keyframes gradientFlow {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

/* Particle System */
.particles-container {
  position: absolute;
  inset: 0;
  z-index: 3;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #FFD700, transparent);
  border-radius: 50%;
  animation: particleFloat 15s linear infinite;
}

@keyframes particleFloat {
  0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

/* Main Content */
.hero-content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
  min-height: 100vh;
  padding: 2rem 0;
}

/* Text Panel */
.hero-text-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  text-align: left;
  animation: slideInLeft 1s ease-out;
}

@keyframes slideInLeft {
  from { transform: translateX(-50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Hero Badge */
.hero-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid #FFD700;
  border-radius: 50px;
  backdrop-filter: blur(10px);
  width: fit-content;
  overflow: hidden;
}

.hero-badge-text {
  color: #FFD700;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  z-index: 2;
}

.hero-badge-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #FFD700, #E60000, #FFD700);
  border-radius: 50px;
  z-index: -1;
  animation: badgeGlow 3s ease-in-out infinite;
}

@keyframes badgeGlow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

/* Hero Title */
.hero-title {
  position: relative;
  margin: 0;
}

.hero-title-main {
  display: block;
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  line-height: 0.9;
  color: #fff;
  text-shadow: 
    0 0 20px rgba(255, 215, 0, 0.5),
    0 0 40px rgba(230, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.hero-title-main::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #FFD700, #E60000, #FFD700);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleShine 4s ease-in-out infinite;
}

@keyframes titleShine {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.hero-title-underline {
  height: 6px;
  background: linear-gradient(90deg, #E60000, #FFD700);
  border-radius: 3px;
  margin-top: 1rem;
  transform: scaleX(0);
  transform-origin: left;
  animation: underlineGrow 1s ease-out 0.5s forwards;
}

@keyframes underlineGrow {
  to { transform: scaleX(1); }
}

/* Hero Subtitle */
.hero-subtitle-container {
  position: relative;
}

.hero-subtitle {
  font-size: clamp(1.25rem, 3vw, 2rem);
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  line-height: 1.4;
  margin: 0;
}

.hero-highlight {
  color: #FFD700;
  font-weight: 600;
  position: relative;
}

.hero-highlight-special {
  color: #E60000;
  font-weight: 700;
  text-shadow: 0 0 10px rgba(230, 0, 0, 0.5);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Hero Location */
.hero-location {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  position: relative;
}

.hero-location-icon {
  font-size: 1.5rem;
  animation: bounce 2s ease-in-out infinite;
}

.hero-location-pulse {
  position: absolute;
  left: 0;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #E60000;
  border-radius: 50%;
  animation: locationPulse 2s ease-out infinite;
}

@keyframes locationPulse {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(2); opacity: 0; }
}

/* Hero Stats */
.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin: 2rem 0;
}

.hero-stat {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 215, 0, 0.2);
  transition: all 0.3s ease;
}

.hero-stat:hover {
  transform: translateY(-5px);
  background: rgba(255, 215, 0, 0.1);
  border-color: #FFD700;
}

.hero-stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  color: #FFD700;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.hero-stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* CTA Buttons */
.hero-cta-container {
  display: flex;
  gap: 1.5rem;
  margin-top: 2rem;
  justify-content: flex-start;
}

.hero-cta-primary,
.hero-cta-secondary {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.hero-cta-primary {
  background: linear-gradient(45deg, #E60000, #FF4444);
  color: white;
  box-shadow: 0 10px 30px rgba(230, 0, 0, 0.3);
}

.hero-cta-secondary {
  background: linear-gradient(45deg, #FFD700, #FFED4E);
  color: #000;
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.hero-cta-primary:hover,
.hero-cta-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(230, 0, 0, 0.4);
}

.hero-cta-secondary:hover {
  box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
}

.hero-cta-ripple,
.hero-cta-glow {
  position: absolute;
  inset: 0;
  border-radius: 50px;
  pointer-events: none;
}

.hero-cta-ripple {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: scale(0);
  animation: ripple 2s ease-out infinite;
}

@keyframes ripple {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(2); opacity: 0; }
}

/* Right Image Panel */
.hero-image-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  animation: slideInRight 1s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.hero-image-simple {
  width: 100%;
  height: 100vh;
  object-fit: cover;
  object-position: center;
}

/* Floating Elements - Removed for cleaner design */

/* Quality Badges - Removed for cleaner design */

/* Scroll Indicator */
.hero-scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  animation: scrollBounce 2s ease-in-out infinite;
  z-index: 20;
}

.hero-scroll-mouse {
  width: 24px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  position: relative;
}

.hero-scroll-wheel {
  width: 4px;
  height: 8px;
  background: rgba(255, 215, 0, 0.8);
  border-radius: 2px;
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  animation: wheelScroll 2s ease-in-out infinite;
}

@keyframes wheelScroll {
  0%, 100% { transform: translateX(-50%) translateY(0); opacity: 1; }
  50% { transform: translateX(-50%) translateY(12px); opacity: 0.3; }
}

.hero-scroll-text {
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.hero-scroll-arrow {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

@keyframes scrollBounce {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-5px); }
}

/* Performance Metrics (Development) */
.hero-metrics {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: #FFD700;
  padding: 1rem;
  border-radius: 8px;
  font-family: monospace;
  font-size: 0.8rem;
  z-index: 9999;
}

.hero-metrics .metric {
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .hero-text-panel {
    text-align: center;
  }

  .hero-cta-container {
    justify-content: center;
  }

  .hero-image-panel {
    height: 60vh;
  }

  .hero-image-simple {
    height: 60vh;
  }

  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .hero-content {
    padding: 0 1rem;
  }

  .hero-text-panel {
    gap: 1.5rem;
    text-align: center;
  }

  .hero-cta-container {
    flex-direction: column;
    align-items: center;
  }

  .hero-image-panel {
    height: 50vh;
  }

  .hero-image-simple {
    height: 50vh;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .hero-title-main {
    font-size: clamp(2.5rem, 6vw, 4rem);
  }

  .hero-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
  }
}
