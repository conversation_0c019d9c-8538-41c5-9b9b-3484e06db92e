# Guida al Deploy - Pizza in Piazza

## 📋 Checklist Pre-Deploy

### ✅ Verifiche Tecniche
- [ ] Compilazione CSS Tailwind completata
- [ ] Test funzionalità JavaScript
- [ ] Verifica responsive design su dispositivi multipli
- [ ] Test click-to-call su mobile
- [ ] Verifica caricamento menù JSON
- [ ] Test filtri categorie menù
- [ ] Controllo performance (PageSpeed Insights)
- [ ] Verifica meta tags SEO

### ✅ Contenuti
- [ ] Verifica dati pizzeria (indirizzo, telefoni, orari)
- [ ] Controllo menù aggiornato
- [ ] Verifica prezzi corretti
- [ ] Test link esterni (Google Maps)
- [ ] Controllo testi e grammatica

## 🚀 Opzioni di Deploy

### 1. Hosting Tradizionale (Consigliato)
**Provider suggeriti**: SiteGround, VHosting, Aruba

#### Preparazione Files
```bash
# Compila CSS per produzione
npx tailwindcss -i ./src/styles/main.css -o ./src/styles/output.css --minify

# Crea cartella dist
mkdir dist
cp -r src/* dist/
```

#### Upload via FTP/SFTP
1. Connettiti al server via FTP
2. Carica tutti i file dalla cartella `src/` nella root del dominio
3. Verifica che `index.html` sia nella root
4. Testa il sito su dominio live

### 2. Netlify (Deploy Automatico)
```bash
# Installa Netlify CLI
npm install -g netlify-cli

# Deploy manuale
netlify deploy --dir=src --prod

# O connetti repository GitHub per deploy automatico
```

### 3. Vercel
```bash
# Installa Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

## 🔧 Configurazioni Server

### Apache (.htaccess)
```apache
# Abilita compressione
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache statico
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>

# Redirect HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### Nginx
```nginx
server {
    listen 80;
    server_name pizzainpiazza.it www.pizzainpiazza.it;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name pizzainpiazza.it www.pizzainpiazza.it;
    
    root /var/www/pizzainpiazza;
    index index.html;
    
    # SSL configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Gzip compression
    gzip on;
    gzip_types text/css application/javascript image/svg+xml;
    
    # Cache headers
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 📊 Post-Deploy Checklist

### ✅ Test Funzionalità
- [ ] Homepage carica correttamente
- [ ] Menu dinamico funziona
- [ ] Filtri categorie attivi
- [ ] Click-to-call funziona su mobile
- [ ] Link Google Maps funziona
- [ ] Responsive design OK su mobile/tablet/desktop
- [ ] Velocità caricamento < 3 secondi

### ✅ SEO e Analytics
- [ ] Google Search Console configurato
- [ ] Google Analytics installato
- [ ] Sitemap.xml generata e inviata
- [ ] Meta tags verificati
- [ ] Structured data validati
- [ ] Test Mobile-Friendly Google

### ✅ Sicurezza
- [ ] Certificato SSL attivo
- [ ] HTTPS redirect funziona
- [ ] Headers sicurezza configurati
- [ ] Backup automatici attivi

## 🔄 Aggiornamenti Futuri

### Menù
Per aggiornare il menù:
1. Modifica `src/data/menu.json`
2. Ricarica il file sul server
3. Il sito si aggiornerà automaticamente

### Contenuti
- Modifica `src/index.html` per testi
- Aggiorna `src/styles/main.css` per stili
- Ricompila con Tailwind se necessario

## 📞 Supporto Tecnico
Per assistenza tecnica contattare:
**AgTech Designe** - Sviluppatori del sito

---
**Dominio suggerito**: pizzainpiazza.it o pizzainpiazza.com
**Email suggerita**: <EMAIL>
