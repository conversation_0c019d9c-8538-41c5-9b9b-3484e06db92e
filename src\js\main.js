// Pizza in Piazza - Main JavaScript File

class PizzaInPiazza {
    constructor() {
        this.menuData = null;
        this.currentCategory = 'all';
        this.init();
    }

    async init() {
        await this.loadMenuData();
        this.setupEventListeners();
        this.renderMenuCategories();
        this.renderMenuItems();
        this.setupScrollEffects();
        this.setupMobileMenu();
        this.setupHeroAnimations();
    }

    // Load menu data from JSON
    async loadMenuData() {
        try {
            showLoading();
            const response = await fetch('./data/menu.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.menuData = await response.json();
        } catch (error) {
            console.error('Error loading menu data:', error);
            showError();
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect and parallax
        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            const scrollY = window.scrollY;

            // Header background effect
            if (scrollY > 100) {
                header.classList.add('bg-white', 'shadow-lg');
            } else {
                header.classList.remove('bg-white', 'shadow-lg');
            }

            // Parallax effect for hero section
            const heroSection = document.getElementById('home');
            if (heroSection && scrollY < window.innerHeight) {
                const parallaxSpeed = 0.5;
                heroSection.style.transform = `translateY(${scrollY * parallaxSpeed}px)`;
            }
        });
    }

    // Setup mobile menu
    setupMobileMenu() {
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking on links
        mobileMenu.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
            });
        });
    }

    // Render menu categories as filter buttons
    renderMenuCategories() {
        if (!this.menuData) return;

        const filtersContainer = document.getElementById('menu-filters');
        const allButton = filtersContainer.querySelector('[data-category="all"]');

        this.menuData.categories.forEach(category => {
            const button = document.createElement('button');
            button.className = 'btn-secondary';
            button.setAttribute('data-category', category.name);
            button.innerHTML = `${category.icon || '📋'} ${category.name}`;
            
            button.addEventListener('click', () => {
                this.filterMenuItems(category.name);
                this.updateActiveFilter(button);
            });

            filtersContainer.appendChild(button);
        });

        // All button event listener
        allButton.addEventListener('click', () => {
            this.filterMenuItems('all');
            this.updateActiveFilter(allButton);
        });
    }

    // Update active filter button
    updateActiveFilter(activeButton) {
        document.querySelectorAll('#menu-filters button').forEach(btn => {
            btn.classList.remove('active', 'bg-pizza-red', 'text-white');
            btn.classList.add('btn-secondary');
        });
        
        activeButton.classList.add('active', 'bg-pizza-red', 'text-white');
        activeButton.classList.remove('btn-secondary');
    }

    // Filter menu items by category
    filterMenuItems(category) {
        this.currentCategory = category;
        this.renderMenuItems();
    }

    // Render menu items
    renderMenuItems() {
        if (!this.menuData) return;

        const menuContainer = document.getElementById('menu-items');
        menuContainer.innerHTML = '';

        let categoriesToShow = this.menuData.categories;
        
        if (this.currentCategory !== 'all') {
            categoriesToShow = this.menuData.categories.filter(cat => cat.name === this.currentCategory);
        }

        categoriesToShow.forEach(category => {
            category.items.forEach(item => {
                const menuItem = this.createMenuItemElement(item, category);
                menuContainer.appendChild(menuItem);
            });
        });

        // Add animation to newly rendered items
        this.animateMenuItems();
    }

    // Create menu item HTML element
    createMenuItemElement(item, category) {
        const div = document.createElement('div');
        div.className = 'menu-item opacity-0 transform translate-y-4';
        
        let priceHTML = '';
        if (item.prices) {
            if (typeof item.prices === 'object') {
                priceHTML = Object.entries(item.prices)
                    .map(([size, price]) => `<span class="text-pizza-red font-bold">${size}: €${price}</span>`)
                    .join(' | ');
            }
        } else if (item.price) {
            priceHTML = `<span class="text-pizza-red font-bold">€${item.price}</span>`;
        }

        div.innerHTML = `
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-lg font-title text-pizza-black">${item.name}</h3>
                <span class="text-xs bg-pizza-gold text-pizza-black px-2 py-1 rounded-full">
                    ${category.icon || '📋'}
                </span>
            </div>
            ${item.ingredients ? `<p class="text-gray-600 text-sm mb-3">${item.ingredients}</p>` : ''}
            ${item.note ? `<p class="text-gray-500 text-xs mb-2 italic">${item.note}</p>` : ''}
            <div class="flex justify-between items-center">
                <div class="text-sm">
                    ${priceHTML}
                </div>
                <button class="btn-primary text-xs py-1 px-3" onclick="window.open('tel:+393468116721')">
                    Ordina
                </button>
            </div>
        `;

        return div;
    }

    // Animate menu items on render
    animateMenuItems() {
        const items = document.querySelectorAll('.menu-item');
        items.forEach((item, index) => {
            setTimeout(() => {
                item.classList.remove('opacity-0', 'translate-y-4');
                item.classList.add('opacity-100', 'translate-y-0', 'transition-all', 'duration-500');
            }, index * 100);
        });
    }

    // Setup scroll effects and animations
    setupScrollEffects() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        // Observe sections for animations
        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });
    }

    // Setup hero section animations
    setupHeroAnimations() {
        // Year counter animation
        this.animateYearCounter();

        // Add stagger animation to hero elements
        const heroElements = document.querySelectorAll('#home .animate-fade-in, #home .animate-slide-up');
        heroElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.2}s`;
        });
    }

    // Animate year counter
    animateYearCounter() {
        const yearElement = document.getElementById('year-counter');
        if (!yearElement) return;

        const targetYear = 2009;
        const currentYear = new Date().getFullYear();
        const yearsInBusiness = currentYear - targetYear;

        let currentCount = 0;
        const increment = targetYear / 50;

        const timer = setInterval(() => {
            currentCount += increment;
            if (currentCount >= targetYear) {
                currentCount = targetYear;
                clearInterval(timer);

                // Add years in business info
                setTimeout(() => {
                    yearElement.innerHTML = `${targetYear} <span class="text-sm opacity-75">(${yearsInBusiness} anni)</span>`;
                }, 1000);
            }
            yearElement.textContent = Math.floor(currentCount);
        }, 50);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PizzaInPiazza();
});

// Utility functions
function formatPhoneNumber(phone) {
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
}

function callRestaurant(phone) {
    window.open(`tel:+39${phone}`, '_self');
}

// Google Maps integration (will be added later)
function initMap() {
    // Google Maps initialization will be implemented in the next phase
    console.log('Google Maps integration ready');
}

// Loading animation for menu
function showLoading() {
    const menuContainer = document.getElementById('menu-items');
    menuContainer.innerHTML = `
        <div class="col-span-full flex justify-center items-center py-12">
            <div class="loading"></div>
            <span class="ml-3 text-gray-600">Caricamento menù...</span>
        </div>
    `;
}

// Error handling for menu loading
function showError() {
    const menuContainer = document.getElementById('menu-items');
    menuContainer.innerHTML = `
        <div class="col-span-full text-center py-12">
            <div class="text-4xl mb-4">😔</div>
            <h3 class="text-xl font-title text-pizza-black mb-2">Ops! Qualcosa è andato storto</h3>
            <p class="text-gray-600 mb-4">Non riusciamo a caricare il menù al momento.</p>
            <button onclick="location.reload()" class="btn-primary">
                🔄 Riprova
            </button>
        </div>
    `;
}

// Scroll to top button
function addScrollToTopButton() {
    const scrollBtn = document.createElement('button');
    scrollBtn.innerHTML = '↑';
    scrollBtn.className = 'fixed bottom-6 right-6 bg-pizza-red text-white w-12 h-12 rounded-full shadow-lg hover:bg-red-700 transition-all duration-300 opacity-0 pointer-events-none z-50';
    scrollBtn.id = 'scroll-to-top';

    scrollBtn.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    document.body.appendChild(scrollBtn);

    // Show/hide scroll button based on scroll position
    window.addEventListener('scroll', () => {
        if (window.scrollY > 300) {
            scrollBtn.classList.remove('opacity-0', 'pointer-events-none');
            scrollBtn.classList.add('opacity-100');
        } else {
            scrollBtn.classList.add('opacity-0', 'pointer-events-none');
            scrollBtn.classList.remove('opacity-100');
        }
    });
}

// Initialize scroll to top button
document.addEventListener('DOMContentLoaded', () => {
    addScrollToTopButton();
});
