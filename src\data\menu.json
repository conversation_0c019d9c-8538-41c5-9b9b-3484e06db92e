{"info": {"name": "Pizza In Piazza", "since": 2009, "address": "<PERSON><PERSON>, 5, <PERSON><PERSON><PERSON>", "phone_numbers": ["3468116721", "3387655879"], "opening_hours": {"days": "Martedì - Domenica", "hours": "11:00-14:30 | 17:30-22:00", "notes": "<PERSON>uso <PERSON> e Domenica mattina"}, "delivery": {"cost": 2.0, "promo": "Ogni Martedì il servizio a domicilio è GRATIS", "areas": "<PERSON><PERSON><PERSON>, Castel San Giovanni, Borgonovo Val Tidone, <PERSON><PERSON><PERSON><PERSON>, Fontana P<PERSON>sa, Rottofreno"}}, "categories": [{"name": "Le Pizze", "icon": "🍕", "items": [{"name": "Margh<PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, origano", "prices": {"media": 6, "maxi": 13}}, {"name": "<PERSON>ra", "ingredients": "pomodoro, aglio, origano, olio", "prices": {"media": 6, "maxi": 13}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "pomodor<PERSON>, fior di latte, mozzarella di bufala", "prices": {"media": 7, "maxi": 15}}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, crudo", "prices": {"media": 7, "maxi": 15}}, {"name": "Prosciutto", "ingredients": "pomodoro, fior di latte, prosciutto", "prices": {"media": 7, "maxi": 15}}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, funghi", "prices": {"media": 7, "maxi": 15}}, {"name": "Napoli", "ingredients": "pomodoro, fior di latte, acci<PERSON>e, or<PERSON>o", "prices": {"media": 7, "maxi": 15}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, salsiccia", "prices": {"media": 7, "maxi": 15}}, {"name": "Genovese", "ingredients": "fior di latte, pesto genovese", "prices": {"media": 6, "maxi": 13}}, {"name": "Affumicata", "ingredients": "pomodoro, fior di latte, scam<PERSON><PERSON>, speck", "prices": {"media": 8, "maxi": 17}}, {"name": "Autunnale", "ingredients": "fior di latte, crema di zucca, funghi, salsiccia", "prices": {"media": 8, "maxi": 17}}, {"name": "Crescenza e Coppa", "ingredients": "pomodoro, fior di latte, crescenza, coppa", "prices": {"media": 8, "maxi": 17}}, {"name": "Diavola Super", "ingredients": "pomodoro, fior di latte, salame piccante, zola", "prices": {"media": 8, "maxi": 17}}, {"name": "Focaccia Tipo <PERSON>", "ingredients": "fior di latte, crescenza", "prices": {"media": 7, "maxi": 15}}, {"name": "Friarielli e Salsiccia", "ingredients": "fior di latte, fria<PERSON><PERSON>, sals<PERSON><PERSON>", "prices": {"media": 8, "maxi": 17}}, {"name": "Frutti di Mare", "ingredients": "pomodoro, fior di latte, frutti di mare, olive, origano", "prices": {"media": 8, "maxi": 17}}, {"name": "Mediterranea", "ingredients": "pomodoro, fior di latte, acci<PERSON>e, olive, pomodorini e origano", "prices": {"media": 8, "maxi": 17}}, {"name": "Pancetta e Scamorza", "ingredients": "fior di latte, pancetta, scamorza affumicata", "prices": {"media": 8, "maxi": 17}}, {"name": "Kebab", "ingredients": "pomodoro, fior di latte, carne kebab, patatine, cipolla, salsa yogurt e piccante", "prices": {"media": 9, "maxi": 19}}, {"name": "Pizza in Piazza", "ingredients": "pomodoro, fior di latte, wurstel, patatine, salsiccia", "prices": {"media": 8, "maxi": 17}}, {"name": "Porcini e Radicchio", "ingredients": "pomodoro, fior di latte, porcini, radicchio", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON>uatt<PERSON>", "ingredients": "fior di latte, brie, zola, em<PERSON><PERSON>", "prices": {"media": 8, "maxi": 17}}, {"name": "Rustica", "ingredients": "pomodoro, fior di latte, funghi, pancetta", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, salmone, panna", "prices": {"media": 8, "maxi": 17}}, {"name": "Tirolese", "ingredients": "pomodoro, fior di latte, speck, zola", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, tonno, cipolle", "prices": {"media": 8, "maxi": 17}}, {"name": "Vegetariana", "ingredients": "pomodor<PERSON>, fior di latte, zucchine, melanzane e peperoni grigliati", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, prosc., funghi, carcio<PERSON>, acci<PERSON>e, capperi, olive", "prices": {"media": 9, "maxi": 19}}, {"name": "Carbonara", "ingredients": "pomodoro, fior di latte, pancetta, uovo, grana", "prices": {"media": 9, "maxi": 19}}, {"name": "Mal<PERSON><PERSON>", "ingredients": "pomo<PERSON><PERSON>, fior di latte, wurs<PERSON>, sals<PERSON><PERSON>, salame piccante, p<PERSON>oni", "prices": {"media": 9, "maxi": 19}}, {"name": "Quattro <PERSON>", "ingredients": "pomodor<PERSON>, fior di latte, pros<PERSON><PERSON>o, fun<PERSON>i, carciofi", "prices": {"media": 9, "maxi": 19}}, {"name": "Tartufata", "ingredients": "crema di tartufo, fior di latte, porcini", "prices": {"media": 9, "maxi": 19}}]}, {"name": "Pizze Speciali", "icon": "⭐", "items": [{"name": "Afrodisiaca", "ingredients": "salsa cocktail, fior di latte, gamberetti, rucola, olio piccante", "prices": {"media": 8, "maxi": 17}}, {"name": "Cheeseburger", "ingredients": "pomodoro, fior di latte, burger, cheddar, cipolla, salsa", "prices": {"media": 9, "maxi": 19}}, {"name": "Fattoria", "ingredients": "p<PERSON><PERSON><PERSON>, fior di latte, metà 4 formaggi, metà salumi assortiti", "prices": {"media": 9, "maxi": 19}}, {"name": "Light", "ingredients": "pomodoro, fior di latte, pomodor<PERSON>, zucchine, philadelphia, rucola", "prices": {"media": 9, "maxi": 19}}, {"name": "Logiciattina", "ingredients": "pomodoro, funghi champ<PERSON>on, sals<PERSON><PERSON>, pomodor<PERSON>, grana", "prices": {"media": 9, "maxi": 19}}, {"name": "Notaro alla Sarmatese", "ingredients": "pomodoro, fior di latte, salame p<PERSON>, rucola, grana, pomodori secchi", "prices": {"media": 9, "maxi": 19}}, {"name": "Pazza", "ingredients": "pomo<PERSON><PERSON>, fior di latte, cipolle, zola, p<PERSON><PERSON>, scamor<PERSON> affumicata", "prices": {"media": 9, "maxi": 19}}, {"name": "Valtellina", "ingredients": "pomodoro, fior di latte, bres<PERSON><PERSON>, rucola, scaglie di grana", "prices": {"media": 9, "maxi": 19}}]}, {"name": "Calzoni", "icon": "🥟", "items": [{"name": "Classico", "ingredients": "pomodoro, fior di latte, prosciutto", "prices": {"media": 8, "maxi": 17}}, {"name": "Farcito", "ingredients": "pomo<PERSON><PERSON>, fior di latte, pros<PERSON><PERSON>o, fun<PERSON>i, carcio<PERSON>, acci<PERSON>e", "prices": {"media": 9, "maxi": 19}}, {"name": "Vegetarian<PERSON>", "ingredients": "pomo<PERSON><PERSON>, fior di latte, zucchine, melanzane, p<PERSON><PERSON> grigliati", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "nutella, (su richiesta fior di latte)", "prices": {"media": 6, "maxi": 12}}]}, {"name": "Burgers", "icon": "🍔", "items": [{"name": "Cheeseburger", "ingredients": "Pane fresco, burger di manzo, pomodori, insalata fresca, cipolla, cheddar e salsa burger", "prices": {"normale": 7.5, "double": 8.5}}, {"name": "Chickenburger", "ingredients": "Pane fresco, pollo croccante, lattuga fresca e maionese", "prices": {"normale": 7.5, "double": 8.5}}]}, {"name": "Snack", "icon": "🍟", "items": [{"name": "Mozza<PERSON>ne", "price": 5.0, "note": "6 pz"}, {"name": "Nuggets", "price": 5.0, "note": "6 pz"}, {"name": "Jalapenos", "price": 5.0, "note": "6 pz"}, {"name": "<PERSON><PERSON> Cipolla", "price": 5.0, "note": "6 pz"}, {"name": "<PERSON>", "price": 5.0, "note": "6 pz"}, {"name": "Rosticelle Mexico", "price": 5.0, "note": "6 pz"}, {"name": "Snack Mix", "price": 9.99, "note": "9/10 pz"}, {"name": "<PERSON><PERSON><PERSON>", "prices": {"piccole": 3.0, "medie": 4.0, "maxi": 5.0}}]}, {"name": "Bibite", "icon": "🥤", "items": [{"name": "Acqua 50cl", "price": 1.0}, {"name": "Lattina 33cl", "price": 2.5}, {"name": "Bottiglia 50cl", "price": 3.5}, {"name": "Bottiglia 1,5L", "price": 4.5}, {"name": "Estathè Brick", "price": 1.2}, {"name": "Redbull 33cl", "price": 3.5}, {"name": "Birra 33cl", "price": 3.5}, {"name": "Birra 66cl", "price": 4.5}]}], "extras": [{"name": "Verdure", "price_increase": 0.5}, {"name": "<PERSON><PERSON><PERSON>", "price_increase": 1.5}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "price_increase": 1.5}, {"name": "<PERSON><PERSON><PERSON> napoli", "price_increase": 1.5}, {"name": "<PERSON><PERSON>", "price_increase": 3.0}, {"name": "<PERSON><PERSON>", "price_increase": 3.0}], "special_options": [{"name": "Formato Baby", "note": "meno 2€ dalla pizza media"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "note": "a soli 1€ in più"}, {"name": "<PERSON><PERSON> Inte<PERSON> o Ka<PERSON>", "note": "+2€ (media) / +3€ (maxi)"}]}