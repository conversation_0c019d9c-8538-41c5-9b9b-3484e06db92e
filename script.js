// Pizza in Piazza - Main JavaScript

class PizzaInPiazza {
    constructor() {
        this.menuData = null;
        this.currentFilter = 'all';
        this.init();
    }

    async init() {
        await this.loadMenuData();
        this.setupNavigation();
        this.setupMenuFilters();
        this.renderMenuItems();
        this.setupScrollEffects();
        this.setupMobileMenu();
    }

    // Load menu data
    async loadMenuData() {
        try {
            const response = await fetch('menu.json');
            this.menuData = await response.json();
        } catch (error) {
            console.error('Error loading menu data:', error);
            this.createFallbackMenu();
        }
    }

    // Create fallback menu if JSON fails to load
    createFallbackMenu() {
        this.menuData = {
            categories: [
                {
                    name: "Le Pizze",
                    items: [
                        {name: "Margherita", ingredients: "pomodoro, fior di latte, origano", prices: {media: 6, maxi: 13}},
                        {name: "<PERSON><PERSON>", ingredients: "pomodoro, aglio, origano, olio", prices: {media: 6, maxi: 13}},
                        {name: "<PERSON><PERSON><PERSON><PERSON>", ingredients: "pomodoro, fior di latte, mozzarella di bufala", prices: {media: 7, maxi: 15}},
                        {name: "Crudo", ingredients: "pomodoro, fior di latte, crudo", prices: {media: 7, maxi: 15}},
                        {name: "Prosciutto", ingredients: "pomodoro, fior di latte, prosciutto", prices: {media: 7, maxi: 15}},
                        {name: "Funghi", ingredients: "pomodoro, fior di latte, funghi", prices: {media: 7, maxi: 15}},
                        {name: "Napoli", ingredients: "pomodoro, fior di latte, acciughe, origano", prices: {media: 7, maxi: 15}},
                        {name: "Salsiccia", ingredients: "pomodoro, fior di latte, salsiccia", prices: {media: 7, maxi: 15}}
                    ]
                },
                {
                    name: "Pizze Speciali",
                    items: [
                        {name: "Quattro Stagioni", ingredients: "pomodoro, fior di latte, prosciutto, funghi, carciofi", prices: {media: 9, maxi: 19}},
                        {name: "Capricciosa", ingredients: "pomodoro, fior di latte, prosc., funghi, carciofi, acciughe, capperi, olive", prices: {media: 9, maxi: 19}},
                        {name: "Diavola", ingredients: "pomodoro, fior di latte, salame piccante", prices: {media: 8, maxi: 17}},
                        {name: "Quattro Formaggi", ingredients: "fior di latte, brie, zola, emmenthal", prices: {media: 8, maxi: 17}}
                    ]
                },
                {
                    name: "Calzoni",
                    items: [
                        {name: "Classico", ingredients: "pomodoro, fior di latte, prosciutto", prices: {media: 8, maxi: 17}},
                        {name: "Farcito", ingredients: "pomodoro, fior di latte, prosciutto, funghi, carciofi", prices: {media: 9, maxi: 19}}
                    ]
                },
                {
                    name: "Burgers",
                    items: [
                        {name: "Cheeseburger", ingredients: "Pane fresco, burger di manzo, cheddar, insalata", prices: {normale: 7.5, double: 8.5}},
                        {name: "Chickenburger", ingredients: "Pane fresco, pollo croccante, lattuga, maionese", prices: {normale: 7.5, double: 8.5}}
                    ]
                },
                {
                    name: "Snack",
                    items: [
                        {name: "Mozzarelline", price: 5.0, note: "6 pz"},
                        {name: "Nuggets", price: 5.0, note: "6 pz"},
                        {name: "Patatine Fritte", prices: {piccole: 3.0, medie: 4.0, maxi: 5.0}}
                    ]
                },
                {
                    name: "Bibite",
                    items: [
                        {name: "Acqua 50cl", price: 1.0},
                        {name: "Lattina 33cl", price: 2.5},
                        {name: "Bottiglia 50cl", price: 3.5},
                        {name: "Birra 33cl", price: 3.5}
                    ]
                }
            ]
        };
    }

    // Setup navigation
    setupNavigation() {
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80; // Account for fixed nav
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Navigation background on scroll - REMOVED
        // Header mantiene sempre il colore originario
    }

    // Setup mobile menu
    setupMobileMenu() {
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');

        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });

        // Close menu when clicking on links
        document.querySelectorAll('.nav-menu a').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
    }

    // Setup menu filters
    setupMenuFilters() {
        const filterButtons = document.querySelectorAll('.filter-btn');

        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                button.classList.add('active');

                // Set current filter
                this.currentFilter = button.dataset.filter;

                // Render filtered items
                this.renderMenuItems();

                // Scroll to first product of selected category
                setTimeout(() => {
                    this.scrollToFirstProduct();
                }, 300); // Wait for render to complete
            });
        });
    }

    // Scroll to first product of current filter
    scrollToFirstProduct() {
        if (this.currentFilter === 'all') {
            // If showing all, scroll to menu section
            const menuSection = document.getElementById('menu');
            if (menuSection) {
                menuSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
            return;
        }

        // Find first product of current category
        const firstProduct = document.querySelector(`.menu-item[data-category="${this.currentFilter}"]`);
        if (firstProduct) {
            firstProduct.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        } else {
            // Fallback: scroll to menu section if no products found
            const menuSection = document.getElementById('menu');
            if (menuSection) {
                menuSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
    }

    // Render menu items
    renderMenuItems() {
        const menuContainer = document.getElementById('menu-items');
        if (!menuContainer || !this.menuData) return;

        menuContainer.innerHTML = '';

        let categoriesToShow = this.menuData.categories;
        
        // Filter categories if not showing all
        if (this.currentFilter !== 'all') {
            categoriesToShow = this.menuData.categories.filter(category => 
                this.getCategoryFilter(category.name) === this.currentFilter
            );
        }

        // Render items from filtered categories
        categoriesToShow.forEach(category => {
            category.items.forEach(item => {
                const menuItem = this.createMenuItemElement(item, category);
                menuContainer.appendChild(menuItem);
            });
        });

        // Add animation to items
        this.animateMenuItems();
    }

    // Get filter name for category
    getCategoryFilter(categoryName) {
        const filterMap = {
            'Le Pizze': 'pizze',
            'Pizze Speciali': 'speciali',
            'Calzoni': 'calzoni',
            'Burgers': 'burger',
            'Snack': 'snack',
            'Bibite': 'bibite'
        };
        return filterMap[categoryName] || 'pizze';
    }

    // Create menu item HTML element
    createMenuItemElement(item, category) {
        const div = document.createElement('div');
        div.className = 'menu-item';

        // Add data-category attribute for scroll targeting
        div.setAttribute('data-category', this.getCategoryFilter(category.name));

        // Add special class for speciali category
        if (category.id === 'speciali') {
            div.classList.add('special');
        }
        
        // Format price display
        let priceHTML = '';
        let hasMaxi = false;
        if (item.prices) {
            if (typeof item.prices === 'object') {
                priceHTML = Object.entries(item.prices)
                    .map(([size, price]) => {
                        if (size.toLowerCase() === 'maxi') {
                            hasMaxi = true;
                            return `${size}: <span class="maxi-price">€${price}</span>`;
                        }
                        return `${size}: €${price}`;
                    })
                    .join(' | ');
            }
        } else if (item.price) {
            priceHTML = `€${item.price}`;
        }

        div.innerHTML = `
            <div class="menu-item-header">
                <h4>${item.name}</h4>
                <span class="menu-item-category">${category.name}</span>
            </div>
            ${item.ingredients ? `<p class="menu-item-description">${item.ingredients}</p>` : ''}
            ${item.note ? `<p class="menu-item-description"><em>${item.note}</em></p>` : ''}
            <div class="menu-item-footer">
                <span class="menu-item-price${hasMaxi ? ' has-maxi' : ''}">${priceHTML}</span>
                <button class="menu-item-order" data-item-name="${item.name}" data-item-prices='${JSON.stringify(item.prices || {default: item.price})}' onclick="addToCartFromButton(this)">
                    Aggiungi al carrello
                </button>
            </div>
        `;

        return div;
    }

    // Animate menu items
    animateMenuItems() {
        const items = document.querySelectorAll('.menu-item');
        items.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Setup scroll effects
    setupScrollEffects() {
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe sections for animations
        document.querySelectorAll('section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'all 0.8s ease';
            observer.observe(section);
        });
    }
}

// Global functions
function orderItem(itemName) {
    const phone = '3468116721';
    const message = `Ciao! Vorrei ordinare: ${itemName}`;
    const whatsappUrl = `https://wa.me/39${phone}?text=${encodeURIComponent(message)}`;
    
    // Try WhatsApp first, fallback to phone call
    if (window.innerWidth <= 768) {
        window.open(whatsappUrl, '_blank');
    } else {
        window.open(`tel:+39${phone}`, '_self');
    }
}

function callRestaurant() {
    window.open('tel:+393468116721', '_self');
}

function orderProduct(productName, price) {
    const phone = '3468116721';
    const message = `Ciao! Vorrei ordinare: ${productName} (€${price})`;
    const whatsappUrl = `https://wa.me/39${phone}?text=${encodeURIComponent(message)}`;

    // Try WhatsApp first, fallback to phone call
    if (window.innerWidth <= 768) {
        window.open(whatsappUrl, '_blank');
    } else {
        window.open(`tel:+39${phone}`, '_self');
    }
}

// Cart functionality
let cart = [];

function addToCartFromButton(button) {
    const itemName = button.getAttribute('data-item-name');
    const pricesJson = button.getAttribute('data-item-prices');
    addToCart(itemName, pricesJson);
}

function addToCart(itemName, pricesJson) {
    try {
        const prices = JSON.parse(pricesJson);

        // If multiple sizes, show size selection
        if (Object.keys(prices).length > 1) {
            showSizeSelection(itemName, prices);
        } else {
            // Single size, add directly
            const price = Object.values(prices)[0];
            const size = Object.keys(prices)[0];
            addItemToCart(itemName, size === 'default' ? '' : size, price);
        }
    } catch (error) {
        console.error('Error parsing prices:', error);
    }
}

function showSizeSelection(itemName, prices) {
    const sizeOptions = Object.entries(prices)
        .map(([size, price]) => `
            <div class="size-option-container">
                <button onclick="selectSizeAndShowExtras('${itemName}', '${size}', ${price})" class="size-option">${size}: €${price}</button>
            </div>
        `).join('');

    const modal = document.createElement('div');
    modal.className = 'size-modal';
    modal.innerHTML = `
        <div class="size-content">
            <h3>Scegli la dimensione per ${itemName}</h3>
            <div class="size-options">
                ${sizeOptions}
            </div>
            <button onclick="closeSizeSelection()" class="btn-secondary">Annulla</button>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function selectSizeAndShowExtras(itemName, size, basePrice) {
    // Close size selection modal
    closeSizeSelection();

    // Show extras selection modal
    showExtrasSelection(itemName, size, basePrice);
}

function showExtrasSelection(itemName, size, basePrice) {
    const extrasOptions = `
        <div class="extras-grid">
            <div class="extra-category">
                <h4>Ingredienti Extra</h4>
                <label class="extra-checkbox">
                    <input type="checkbox" data-extra="verdure" data-price="0.5">
                    <span>Verdure 🍅 (+€0,50)</span>
                </label>
                <label class="extra-checkbox">
                    <input type="checkbox" data-extra="formaggi" data-price="1.5">
                    <span>Formaggi 🧀 (+€1,50)</span>
                </label>
                <label class="extra-checkbox">
                    <input type="checkbox" data-extra="affettati" data-price="1.5" id="affettati-checkbox">
                    <span>Affettati 🔪 (+€1,50)</span>
                </label>
                <div class="extra-note-container" id="affettati-note" style="display: none;">
                    <label for="affettati-input">Specifica quale affettato:</label>
                    <input type="text" id="affettati-input" placeholder="es. Prosciutto crudo, Salame, Speck..." class="extra-note-input">
                </div>

                <div class="extra-note-container" id="general-note">
                    <label for="extra-notes-input">Note aggiuntive per gli ingredienti:</label>
                    <textarea id="extra-notes-input" placeholder="Aggiungi note specifiche per gli ingredienti extra..." class="extra-note-textarea"></textarea>
                </div>
            </div>

            <div class="extra-category">
                <h4>Impasto Speciale</h4>
                <label class="extra-radio">
                    <input type="radio" name="impasto" data-extra="normale" data-price="0" checked>
                    <span>Normale (€0,00)</span>
                </label>
                <label class="extra-radio">
                    <input type="radio" name="impasto" data-extra="integrale" data-price="${size.toLowerCase() === 'media' ? '2' : '3'}">
                    <span>Integrale/Kamut (+€${size.toLowerCase() === 'media' ? '2,00' : '3,00'})</span>
                </label>
            </div>

            <div class="extra-category">
                <h4>Forma e Bordo</h4>
                <label class="extra-checkbox">
                    <input type="checkbox" data-extra="cuoricina" data-price="1">
                    <span>Cuoricina ❤️ (+€1,00)</span>
                </label>
                <label class="extra-checkbox">
                    <input type="checkbox" data-extra="napoli" data-price="1.5">
                    <span>Tipo Napoli (+€1,50)</span>
                </label>
                <label class="extra-checkbox">
                    <input type="checkbox" data-extra="bordo-ripieno" data-price="3">
                    <span>Bordo Ripieno (+€3,00)</span>
                </label>
                <label class="extra-checkbox">
                    <input type="checkbox" data-extra="burrata" data-price="3">
                    <span>Burrata (+€3,00)</span>
                </label>
            </div>
        </div>
    `;

    const modal = document.createElement('div');
    modal.className = 'extras-modal';
    modal.innerHTML = `
        <div class="extras-content">
            <h3>Personalizza ${itemName} (${size})</h3>
            <div class="base-price">Prezzo base: €${basePrice.toFixed(2)}</div>
            ${extrasOptions}
            <div class="extras-total">
                <strong>Totale: €<span id="extras-total-price">${basePrice.toFixed(2)}</span></strong>
            </div>
            <div class="extras-actions">
                <button onclick="closeExtrasSelection()" class="btn-secondary">Annulla</button>
                <button onclick="addItemWithExtras('${itemName}', '${size}', ${basePrice})" class="btn-primary">Aggiungi al carrello</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';

    // Setup price calculation
    setupExtrasCalculation(basePrice);
}

function closeSizeSelection() {
    const modal = document.querySelector('.size-modal');
    if (modal) {
        modal.remove();
    }
}

function closeExtrasSelection() {
    const modal = document.querySelector('.extras-modal');
    if (modal) {
        modal.remove();
    }
}

function setupExtrasCalculation(basePrice) {
    const modal = document.querySelector('.extras-modal');
    const checkboxes = modal.querySelectorAll('input[type="checkbox"], input[type="radio"]');
    const totalElement = document.getElementById('extras-total-price');
    const affettatiCheckbox = document.getElementById('affettati-checkbox');
    const affettatiNote = document.getElementById('affettati-note');

    // Handle affettati checkbox to show/hide note field
    if (affettatiCheckbox) {
        affettatiCheckbox.addEventListener('change', () => {
            if (affettatiCheckbox.checked) {
                affettatiNote.style.display = 'block';
            } else {
                affettatiNote.style.display = 'none';
                document.getElementById('affettati-input').value = '';
            }
        });
    }

    checkboxes.forEach(input => {
        input.addEventListener('change', () => {
            let total = basePrice;

            // Calculate extras total
            const selectedExtras = modal.querySelectorAll('input:checked');
            selectedExtras.forEach(extra => {
                total += parseFloat(extra.dataset.price);
            });

            totalElement.textContent = total.toFixed(2);
        });
    });
}

function addItemWithExtras(itemName, size, basePrice) {
    const modal = document.querySelector('.extras-modal');
    const selectedExtras = modal.querySelectorAll('input:checked');
    const affettatiInput = document.getElementById('affettati-input');
    const extraNotesInput = document.getElementById('extra-notes-input');

    let totalPrice = basePrice;
    let extrasDescription = [];
    let notes = [];

    selectedExtras.forEach(extra => {
        if (extra.dataset.price > 0) {
            totalPrice += parseFloat(extra.dataset.price);
            let extraName = extra.nextElementSibling.textContent.split('(')[0].trim();

            // Add specific affettato note if provided
            if (extra.dataset.extra === 'affettati' && affettatiInput.value.trim()) {
                extraName += ` (${affettatiInput.value.trim()})`;
            }

            extrasDescription.push(extraName);
        }
    });

    // Add general notes if provided
    if (extraNotesInput.value.trim()) {
        notes.push(`Note: ${extraNotesInput.value.trim()}`);
    }

    // Create item name with extras and notes
    let itemNameWithExtras = itemName;
    if (extrasDescription.length > 0) {
        itemNameWithExtras += ` (${extrasDescription.join(', ')})`;
    }
    if (notes.length > 0) {
        itemNameWithExtras += ` - ${notes.join(' | ')}`;
    }

    // Add to cart
    addItemToCart(itemNameWithExtras, size, totalPrice);

    // Close modal
    closeExtrasSelection();
}

function addItemToCart(itemName, size, price) {
    const existingItem = cart.find(item => item.name === itemName && item.size === size);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            name: itemName,
            size: size,
            price: parseFloat(price),
            quantity: 1
        });
    }

    updateCartUI();
    showCartNotification();
}

function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartUI();
}

function updateQuantity(index, change) {
    cart[index].quantity += change;
    if (cart[index].quantity <= 0) {
        removeFromCart(index);
    } else {
        updateCartUI();
    }
}

function clearCart() {
    cart = [];
    updateCartUI();
}

function updateCartUI() {
    const cartCount = document.getElementById('cart-count');
    const cartItems = document.getElementById('cart-items');
    const cartEmpty = document.getElementById('cart-empty');
    const cartTotal = document.getElementById('cart-total');

    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    cartCount.textContent = totalItems;
    cartTotal.textContent = totalPrice.toFixed(2);

    if (cart.length === 0) {
        cartItems.style.display = 'none';
        cartEmpty.style.display = 'block';
    } else {
        cartItems.style.display = 'block';
        cartEmpty.style.display = 'none';

        cartItems.innerHTML = cart.map((item, index) => `
            <div class="cart-item">
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    ${item.size ? `<div class="cart-item-size">${item.size}</div>` : ''}
                </div>
                <div class="cart-item-controls">
                    <div class="cart-item-quantity">
                        <button class="quantity-btn" onclick="updateQuantity(${index}, -1)">-</button>
                        <span class="quantity-display">${item.quantity}</span>
                        <button class="quantity-btn" onclick="updateQuantity(${index}, 1)">+</button>
                    </div>
                    <div class="cart-item-price">€${(item.price * item.quantity).toFixed(2)}</div>
                </div>
            </div>
        `).join('');
    }
}

function toggleCart() {
    const modal = document.getElementById('cart-modal');
    modal.style.display = modal.style.display === 'block' ? 'none' : 'block';
}

function showCartNotification() {
    // Simple notification that item was added
    const notification = document.createElement('div');
    notification.className = 'cart-notification';
    notification.textContent = 'Prodotto aggiunto al carrello!';
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--primary-red);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function checkout() {
    if (cart.length === 0) {
        alert('Il carrello è vuoto!');
        return;
    }

    const phone = '3468116721';
    const orderDetails = cart.map(item =>
        `${item.name}${item.size ? ` (${item.size})` : ''} x${item.quantity} - €${(item.price * item.quantity).toFixed(2)}`
    ).join('\n');

    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const message = `Ciao! Vorrei ordinare:\n\n${orderDetails}\n\nTotale: €${total.toFixed(2)}`;

    const whatsappUrl = `https://wa.me/39${phone}?text=${encodeURIComponent(message)}`;

    window.open(whatsappUrl, '_blank');

    // Clear cart after order
    clearCart();
    toggleCart();
}

function filterMenu(category) {
    // Scroll to menu section
    const menuSection = document.getElementById('menu');
    if (menuSection) {
        menuSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Wait for scroll to complete, then filter
    setTimeout(() => {
        // Find and click the appropriate filter button
        const filterBtn = document.querySelector(`[data-filter="${category}"]`);
        if (filterBtn) {
            filterBtn.click();
        }
    }, 800);
}

function scrollToOffers() {
    const offersSection = document.querySelector('.offers');
    if (offersSection) {
        offersSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Carousel functionality
let currentSlide = 0;
const totalSlides = 6;

function scrollCarousel(direction) {
    const track = document.getElementById('carousel-track');
    const cardWidth = 280 + 32; // card width + gap

    if (direction === 'left') {
        currentSlide = currentSlide > 0 ? currentSlide - 1 : totalSlides - 1;
    } else {
        currentSlide = currentSlide < totalSlides - 1 ? currentSlide + 1 : 0;
    }

    track.scrollTo({
        left: currentSlide * cardWidth,
        behavior: 'smooth'
    });

    updateIndicators();
}

function goToSlide(slideIndex) {
    currentSlide = slideIndex;
    const track = document.getElementById('carousel-track');
    const cardWidth = 280 + 32; // card width + gap

    track.scrollTo({
        left: currentSlide * cardWidth,
        behavior: 'smooth'
    });

    updateIndicators();
}

function updateIndicators() {
    const indicators = document.querySelectorAll('.indicator');
    indicators.forEach((indicator, index) => {
        if (index === currentSlide) {
            indicator.classList.add('active');
        } else {
            indicator.classList.remove('active');
        }
    });
}

// Auto-scroll carousel
function autoScrollCarousel() {
    scrollCarousel('right');
}

// Initialize carousel auto-scroll
let carouselInterval;

function startCarouselAutoScroll() {
    carouselInterval = setInterval(autoScrollCarousel, 5000); // 5 seconds
}

function stopCarouselAutoScroll() {
    clearInterval(carouselInterval);
}

// Vertical Carousel functionality
let currentVerticalSlide = 0;
const totalVerticalSlides = 7; // Aggiornato per 7 immagini totali

function scrollVerticalCarousel(direction) {
    const track = document.getElementById('vertical-carousel-track');
    if (!track) return;

    const slideHeight = track.offsetHeight;

    if (direction === 'up') {
        currentVerticalSlide = currentVerticalSlide > 0 ? currentVerticalSlide - 1 : totalVerticalSlides - 1;
    } else {
        currentVerticalSlide = currentVerticalSlide < totalVerticalSlides - 1 ? currentVerticalSlide + 1 : 0;
    }

    track.style.transform = `translateY(-${currentVerticalSlide * slideHeight}px)`;
}

// Auto-scroll vertical carousel
function autoScrollVerticalCarousel() {
    scrollVerticalCarousel('down');
}

// Start auto-scroll when page loads
document.addEventListener('DOMContentLoaded', () => {
    startCarouselAutoScroll();

    // Pause auto-scroll on hover
    const carouselContainer = document.querySelector('.carousel-container');
    if (carouselContainer) {
        carouselContainer.addEventListener('mouseenter', stopCarouselAutoScroll);
        carouselContainer.addEventListener('mouseleave', startCarouselAutoScroll);
    }

    // Handle scroll events for indicators
    const track = document.getElementById('carousel-track');
    if (track) {
        track.addEventListener('scroll', () => {
            const cardWidth = 280 + 32;
            const scrollLeft = track.scrollLeft;
            const newSlide = Math.round(scrollLeft / cardWidth);

            if (newSlide !== currentSlide) {
                currentSlide = newSlide;
                updateIndicators();
            }
        });
    }

    // Start vertical carousel auto-scroll
    setInterval(autoScrollVerticalCarousel, 3000); // 3 seconds

    // Initialize privacy popup
    initPrivacyPopup();
});

// ===== PRIVACY & COOKIE MANAGEMENT =====
function initPrivacyPopup() {
    // Check if user has already made a choice
    const consent = getCookie('privacy_consent');
    if (!consent) {
        showPrivacyPopup();
    }
}

function showPrivacyPopup() {
    const popup = document.getElementById('privacy-popup');
    if (popup) {
        popup.classList.add('show');

        // Add click outside to close (only for cookie settings, not main popup)
        popup.addEventListener('click', function(e) {
            if (e.target === popup && popup.id === 'cookie-settings') {
                closeCookieSettings();
            }
        });
    }
}

function hidePrivacyPopup() {
    const popup = document.getElementById('privacy-popup');
    if (popup) {
        popup.classList.remove('show');
    }
}

function acceptAllCookies() {
    setCookie('privacy_consent', 'all', 365);
    setCookie('analytics_consent', 'true', 365);
    hidePrivacyPopup();
    showNotification('Tutti i cookie sono stati accettati');
    loadAnalytics();
}

function rejectCookies() {
    setCookie('privacy_consent', 'necessary', 365);
    setCookie('analytics_consent', 'false', 365);
    hidePrivacyPopup();
    showNotification('Solo cookie necessari accettati');
}

function showCookieSettings() {
    const modal = document.getElementById('cookie-settings');
    if (modal) {
        modal.style.display = 'flex';

        // Set current settings
        const analyticsConsent = getCookie('analytics_consent');
        const analyticsCheckbox = document.getElementById('analytics-cookies');
        if (analyticsCheckbox) {
            analyticsCheckbox.checked = analyticsConsent === 'true';
        }

        // Add click outside to close
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeCookieSettings();
            }
        });
    }
}

function closeCookieSettings() {
    const modal = document.getElementById('cookie-settings');
    if (modal) {
        modal.style.display = 'none';
    }
}

function saveCookieSettings() {
    const analyticsCheckbox = document.getElementById('analytics-cookies');
    const analyticsConsent = analyticsCheckbox ? analyticsCheckbox.checked : false;

    setCookie('privacy_consent', 'custom', 365);
    setCookie('analytics_consent', analyticsConsent.toString(), 365);

    hidePrivacyPopup();
    closeCookieSettings();

    if (analyticsConsent) {
        loadAnalytics();
    }

    showNotification('Preferenze cookie salvate');
}

function setCookie(name, value, days) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
}

function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

function loadAnalytics() {
    // Placeholder for analytics loading
    console.log('Analytics cookies accepted - Analytics would be loaded here');
    // Example: Google Analytics implementation
    /*
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID';
    document.head.appendChild(script);
    */
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'privacy-notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--primary-red);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        z-index: 4000;
        animation: slideIn 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Function to reset privacy settings (for testing)
function resetPrivacySettings() {
    setCookie('privacy_consent', '', -1);
    setCookie('analytics_consent', '', -1);
    showNotification('Impostazioni privacy resettate');
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Function to check current privacy status
function checkPrivacyStatus() {
    const consent = getCookie('privacy_consent');
    const analytics = getCookie('analytics_consent');
    console.log('Privacy Consent:', consent);
    console.log('Analytics Consent:', analytics);
    return { consent, analytics };
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PizzaInPiazza();
});

// Add mobile menu styles
const mobileMenuStyles = `
    @media (max-width: 768px) {
        .nav-menu {
            position: fixed;
            top: 80px;
            left: -100%;
            width: 100%;
            height: calc(100vh - 80px);
            background: white;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            padding-top: 2rem;
            transition: left 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .nav-menu.active {
            left: 0;
        }
        
        .nav-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }
        
        .nav-toggle.active span:nth-child(2) {
            opacity: 0;
        }
        
        .nav-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }
    }
`;

// Inject mobile menu styles
const styleSheet = document.createElement('style');
styleSheet.textContent = mobileMenuStyles;
document.head.appendChild(styleSheet);
