// Enterprise Hero Section - Advanced JavaScript

class EnterpriseHero {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.particles = [];
        this.animationId = null;
        this.performanceMetrics = {
            fps: 0,
            lastTime: 0,
            frameCount: 0
        };
        
        this.init();
    }

    init() {
        this.setupCanvas();
        this.createParticleSystem();
        this.setupCounterAnimations();
        this.setupScrollEffects();
        this.setupPerformanceMonitoring();
        this.startAnimation();
        
        // Preload optimizations
        this.preloadAssets();
        this.setupIntersectionObserver();
    }

    setupCanvas() {
        this.canvas = document.getElementById('hero-canvas');
        if (!this.canvas) return;
        
        this.ctx = this.canvas.getContext('2d', { alpha: true });
        this.resizeCanvas();
        
        window.addEventListener('resize', () => this.resizeCanvas());
    }

    resizeCanvas() {
        if (!this.canvas) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        this.ctx.scale(dpr, dpr);
    }

    createParticleSystem() {
        const particleCount = this.isMobile() ? 30 : 60;
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push(this.createParticle());
        }
        
        // Create DOM particles for better performance on some devices
        this.createDOMParticles();
    }

    createParticle() {
        return {
            x: Math.random() * window.innerWidth,
            y: window.innerHeight + Math.random() * 100,
            size: Math.random() * 3 + 1,
            speed: Math.random() * 2 + 0.5,
            opacity: Math.random() * 0.5 + 0.2,
            color: Math.random() > 0.5 ? '#FFD700' : '#E60000',
            angle: Math.random() * Math.PI * 2,
            spin: (Math.random() - 0.5) * 0.02
        };
    }

    createDOMParticles() {
        const container = document.getElementById('particles-container');
        if (!container) return;
        
        const particleCount = this.isMobile() ? 15 : 25;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 15 + 's';
            particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
            container.appendChild(particle);
        }
    }

    updateParticles() {
        if (!this.ctx) return;
        
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.particles.forEach(particle => {
            // Update position
            particle.y -= particle.speed;
            particle.x += Math.sin(particle.angle) * 0.5;
            particle.angle += particle.spin;
            
            // Reset particle if it goes off screen
            if (particle.y < -10) {
                particle.y = window.innerHeight + 10;
                particle.x = Math.random() * window.innerWidth;
            }
            
            // Draw particle
            this.ctx.save();
            this.ctx.globalAlpha = particle.opacity;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }

    setupCounterAnimations() {
        const counters = document.querySelectorAll('.hero-stat-number');
        
        counters.forEach(counter => {
            const target = parseInt(counter.dataset.count);
            const duration = 2000;
            const increment = target / (duration / 16);
            let current = 0;
            
            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };
            
            // Start animation when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setTimeout(updateCounter, Math.random() * 500);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(counter);
        });
    }

    setupScrollEffects() {
        let ticking = false;
        
        const updateScrollEffects = () => {
            const scrollY = window.scrollY;
            const heroSection = document.getElementById('home');
            
            if (heroSection && scrollY < window.innerHeight) {
                const progress = scrollY / window.innerHeight;
                
                // Parallax effect
                heroSection.style.transform = `translateY(${scrollY * 0.5}px)`;
                
                // Fade effect
                heroSection.style.opacity = 1 - progress * 0.5;
                
                // Scale effect for image
                const heroImage = document.querySelector('.hero-image');
                if (heroImage) {
                    heroImage.style.transform = `scale(${1 + progress * 0.1})`;
                }
            }
            
            ticking = false;
        };
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        });
    }

    setupPerformanceMonitoring() {
        let frameCount = 0;
        let lastTime = performance.now();
        
        const updateFPS = (currentTime) => {
            frameCount++;
            
            if (currentTime - lastTime >= 1000) {
                this.performanceMetrics.fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;
                
                // Update metrics display (if enabled)
                const fpsDisplay = document.querySelector('[data-metric="fps"] span');
                if (fpsDisplay) {
                    fpsDisplay.textContent = this.performanceMetrics.fps;
                }
                
                // Adaptive quality based on performance
                this.adaptiveQuality();
            }
        };
        
        const animate = (currentTime) => {
            updateFPS(currentTime);
            this.animationId = requestAnimationFrame(animate);
        };
        
        animate(performance.now());
    }

    adaptiveQuality() {
        const fps = this.performanceMetrics.fps;
        
        if (fps < 30) {
            // Reduce particle count for better performance
            this.particles = this.particles.slice(0, Math.floor(this.particles.length * 0.7));
            
            // Disable some animations
            document.documentElement.style.setProperty('--animation-enabled', '0');
        } else if (fps > 55) {
            // Increase quality if performance allows
            if (this.particles.length < 60) {
                this.particles.push(this.createParticle());
            }
            
            document.documentElement.style.setProperty('--animation-enabled', '1');
        }
    }

    setupIntersectionObserver() {
        const heroElements = document.querySelectorAll('.hero-text-panel > *, .hero-visual-panel > *');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        heroElements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'all 0.6s ease-out';
            observer.observe(element);
        });
    }

    preloadAssets() {
        // Preload hero image
        const heroImage = document.querySelector('.hero-image');
        if (heroImage && heroImage.src) {
            const img = new Image();
            img.src = heroImage.src;
        }
        
        // Preload fonts
        if ('fonts' in document) {
            document.fonts.load('900 3rem "Anton"');
            document.fonts.load('300 1.25rem "Roboto"');
        }
    }

    startAnimation() {
        const animate = () => {
            this.updateParticles();
            this.animationId = requestAnimationFrame(animate);
        };
        
        animate();
    }

    isMobile() {
        return window.innerWidth < 768;
    }

    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // Clean up event listeners
        window.removeEventListener('resize', this.resizeCanvas);
        window.removeEventListener('scroll', this.updateScrollEffects);
    }
}

// Global functions for CTA buttons
function scrollToMenu() {
    const menuSection = document.getElementById('menu');
    if (menuSection) {
        menuSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function callRestaurant(phone) {
    window.open(`tel:+39${phone}`, '_self');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Add small delay to ensure all elements are rendered
    setTimeout(() => {
        window.enterpriseHero = new EnterpriseHero();
    }, 100);
});

// Performance optimization: Pause animations when tab is not visible
document.addEventListener('visibilitychange', () => {
    if (window.enterpriseHero) {
        if (document.hidden) {
            cancelAnimationFrame(window.enterpriseHero.animationId);
        } else {
            window.enterpriseHero.startAnimation();
        }
    }
});
