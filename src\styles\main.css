@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Anton:wght@400&family=Oswald:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Lato:wght@300;400;700&display=swap');

/* Custom Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Roboto', 'Lato', sans-serif;
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: '<PERSON>', '<PERSON>', sans-serif;
    font-weight: 400;
    letter-spacing: 0.5px;
  }
}

/* Custom Components */
@layer components {
  .btn-primary {
    @apply bg-pizza-red hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-pizza-gold hover:bg-yellow-500 text-pizza-black font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-hero {
    @apply relative overflow-hidden;
  }

  .btn-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-hero:hover::before {
    left: 100%;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-xl;
  }
  
  .menu-item {
    @apply bg-white rounded-lg shadow-md p-4 transition-all duration-300 hover:shadow-lg hover:transform hover:scale-105;
  }
  
  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto;
  }
}

/* Custom Utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .text-shadow-strong {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  }

  .bg-gradient-pizza {
    background: linear-gradient(135deg, #E60000 0%, #FFD700 100%);
  }

  .bg-gradient-hero {
    background: linear-gradient(135deg, #000000 0%, #E60000 50%, #FFD700 100%);
  }

  .hero-gradient {
    background: linear-gradient(135deg, #000000 0%, #E60000 50%, #FFD700 100%);
    background-size: 400% 400%;
    animation: gradientShift 10s ease infinite;
  }

  .hero-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(0,0,0,0.8) 0%,
      rgba(230,0,0,0.6) 30%,
      rgba(255,215,0,0.4) 70%,
      rgba(0,0,0,0.8) 100%);
    animation: gradientPulse 6s ease-in-out infinite;
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  @keyframes gradientPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.1; }
  }

  .bg-overlay {
    background: rgba(0, 0, 0, 0.5);
  }

  .hero-image-glow {
    box-shadow:
      0 0 50px rgba(255, 215, 0, 0.4),
      0 0 100px rgba(230, 0, 0, 0.3),
      0 0 150px rgba(255, 215, 0, 0.2);
  }

  .pizza-border-glow {
    border: 4px solid #FFD700;
    box-shadow:
      0 0 20px rgba(255, 215, 0, 0.6),
      inset 0 0 20px rgba(255, 215, 0, 0.2);
  }

  .floating-animation {
    animation: floating 3s ease-in-out infinite;
  }

  .typing-animation {
    overflow: hidden;
    border-right: 3px solid #FFD700;
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
  }

  @keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes typing {
    from { width: 0; }
    to { width: 100%; }
  }

  @keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #FFD700; }
  }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Typography */
@media (max-width: 640px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}
