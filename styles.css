/* ===== RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-red: #E60000;
    --primary-gold: #FFD700;
    --dark: #1a1a1a;
    --light: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-600: #6c757d;
    --gray-800: #343a40;
    
    /* Typography */
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Inter', sans-serif;
    
    /* Spacing */
    --section-padding: 5rem 0;
    --container-padding: 0 1rem;
    --border-radius: 8px;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 24px rgba(0,0,0,0.2);
    
    /* Transitions */
    --transition: all 0.3s ease;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--light);
    background:
        radial-gradient(circle at 20% 80%, rgba(230, 0, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(230, 0, 0, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #1a1a1a 0%, #2a1a1a 25%, #3a1a1a 50%, #E60000 75%, #FFD700 100%);
    background-size: 400% 400%, 300% 300%, 500% 500%, 100% 100%;
    background-attachment: fixed;
    animation: projectGradient 20s ease infinite;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 85%, rgba(230, 0, 0, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 85% 15%, rgba(255, 215, 0, 0.1) 0%, transparent 40%),
        linear-gradient(45deg, rgba(230, 0, 0, 0.05) 0%, rgba(255, 215, 0, 0.05) 100%);
    z-index: -2;
    animation: projectPulse 6s ease-in-out infinite;
    pointer-events: none;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, #E60000 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, #FFD700 1px, transparent 1px),
        radial-gradient(circle at 50% 50%, #ffffff 0.5px, transparent 0.5px);
    background-size: 50px 50px, 80px 80px, 30px 30px;
    opacity: 0.1;
    z-index: -1;
    animation: dotPattern 25s linear infinite;
    pointer-events: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: clamp(2.1rem, 4.2vw, 3.4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--gray-600);
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--gray-800);
}

.highlight {
    background: linear-gradient(45deg, #E60000, #ffffff, #E60000);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 900;
    animation: projectShift 4s ease infinite;
    text-shadow: none;
}

@keyframes projectShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ===== BUTTONS ===== */
.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    font-size: 0.8rem;
}

.btn-primary {
    background: var(--primary-red);
    color: var(--light);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: #cc0000;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--light);
    color: var(--dark);
    border: 2px solid var(--gray-300);
}

.btn-secondary:hover {
    border-color: var(--primary-red);
    color: var(--primary-red);
    transform: translateY(-2px);
}

/* ===== NAVIGATION ===== */
.nav {
    position: fixed;
    top: 0;
    width: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(230, 0, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(230, 0, 0, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #1a1a1a 0%, #2a1a1a 25%, #3a1a1a 50%, #E60000 75%, #FFD700 100%);
    background-size: 400% 400%, 300% 300%, 500% 500%, 100% 100%;
    animation: projectGradient 20s ease infinite;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.nav-logo {
    height: 50px;
    width: auto;
    max-width: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    object-fit: contain;
}

/* Nav brand text removed - logo only */

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--light);
    font-weight: 500;
    transition: var(--transition);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.nav-menu a:hover {
    color: var(--primary-gold);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 10px rgba(255, 215, 0, 0.5);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--light);
    transition: var(--transition);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 60vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

@keyframes projectGradient {
    0%, 100% {
        background-position: 0% 50%, 0% 50%, 0% 50%, 0% 50%;
    }
    25% {
        background-position: 100% 50%, 25% 75%, 50% 0%, 0% 50%;
    }
    50% {
        background-position: 50% 100%, 75% 25%, 100% 50%, 0% 50%;
    }
    75% {
        background-position: 0% 0%, 50% 100%, 25% 75%, 0% 50%;
    }
}

/* Hero overlays removed - using global body overlays */

@keyframes projectPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

@keyframes dotPattern {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Floating shapes removed - using project palette only */

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero .container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 0;
    padding: 1.5rem;
    border: none;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 40px rgba(255, 215, 0, 0.2);
    overflow: hidden;
    position: relative;
    margin: 0;
    max-width: 100%;
    width: 100%;
}

.hero .container::before {
    content: '';
    position: absolute;
    top: -20%;
    left: -20%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%
    );
    animation: glassReflection 8s ease infinite;
    pointer-events: none;
}

.hero-text {
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Hero badge removed for Gen Z design */

.hero-title {
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow:
        0 0 8px rgba(255, 255, 255, 0.8),
        0 0 15px rgba(255, 215, 0, 0.6),
        0 0 20px rgba(230, 0, 0, 0.4),
        2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: projectGlow 3s ease-in-out infinite alternate;
    font-size: clamp(1.8rem, 3.5vw, 2.8rem);
}

@keyframes projectGlow {
    from {
        text-shadow:
            0 0 10px rgba(255, 255, 255, 0.8),
            0 0 20px rgba(255, 215, 0, 0.6),
            0 0 30px rgba(230, 0, 0, 0.4),
            2px 2px 4px rgba(0, 0, 0, 0.8);
    }
    to {
        text-shadow:
            0 0 15px rgba(255, 255, 255, 1),
            0 0 25px rgba(255, 215, 0, 0.8),
            0 0 35px rgba(230, 0, 0, 0.6),
            2px 2px 4px rgba(0, 0, 0, 0.8);
    }
}

.hero-description {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    max-width: 400px;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.hero-nav-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.nav-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    padding: 0.8rem 0.6rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid transparent;
    border-radius: 12px;
    backdrop-filter: blur(20px);
    cursor: pointer;
    transition: all 0.4s ease;
    color: white;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg, #E60000, #FFD700, #E60000, #FFD700);
    background-size: 300% 300%;
    border-radius: 15px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: projectBorderGlow 3s ease infinite;
}

@keyframes projectBorderGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.nav-btn:hover {
    background: rgba(255, 215, 0, 0.1);
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 10px 30px rgba(255, 215, 0, 0.3),
        0 0 20px rgba(230, 0, 0, 0.2);
}

.nav-btn:active {
    transform: translateY(-2px) scale(1.02);
    background: rgba(230, 0, 0, 0.1);
}

.nav-btn-icon {
    font-size: 1.2rem;
    margin-bottom: 0.2rem;
}

.nav-btn-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: stretch;
    padding: 0.5rem;
}

.hero-image img {
    width: 200%;
    max-width: 250px;
    height: auto;
    object-fit: contain;
    border-radius: 1px;
    transform: translate(165px, 30px);
}

/* ===== VERTICAL OFFERS CAROUSEL ===== */
/*
ESEMPI DI POSIZIONAMENTO:

1. Spostare a sinistra:
   --vertical-carousel-right: auto;
   --vertical-carousel-left: 50px;

2. Centrare orizzontalmente:
   --vertical-carousel-left: 50%;
   --vertical-carousel-x: -60px; (metà della larghezza)

3. Spostare in alto:
   --vertical-carousel-top: 20px;

4. Spostare con transform:
   --vertical-carousel-x: 100px; (sposta a destra)
   --vertical-carousel-y: -50px; (sposta in alto)
*/

.vertical-offers-carousel {
    position: absolute;
    width: var(--vertical-carousel-width, 150px);
    height: var(--vertical-carousel-height, 300px);

    /* POSIZIONAMENTO PERSONALIZZABILE - MODIFICA QUESTI VALORI */
    top: var(--vertical-carousel-top, 80px);        /* Spostamento verticale (+30px) */
    right: var(--vertical-carousel-right, 350px);    /* Spostamento orizzontale da destra */
    /* Oppure usa left invece di right: */
    /* left: var(--vertical-carousel-left, 50px); */

    z-index: 10;

    /* Per spostare facilmente, puoi anche usare transform */
    transform: translate(var(--vertical-carousel-x, 10px), var(--vertical-carousel-y, -120px));
}

.vertical-carousel-container {
    position: relative;
    width: 200%;
    height: 200%;
    overflow: hidden;
    /* Rimosso background, blur e ombre per mostrare solo immagini */
}

.vertical-carousel-track {
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: transform 0.5s ease;
}

.offer-slide {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    text-align: center;
}

.offer-slide img {
    width: 100%;
    height: auto;
    max-height: 220px;
    object-fit: contain;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    /* Rimosso drop-shadow per look più pulito */
}

.product-name-vertical {
    color: var(--light);
    font-size: 0.8rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    /* Rimosso background, padding, border per look minimalista */
}

.vertical-carousel-controls {
    position: absolute;
    right: -40px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.vertical-carousel-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    background: rgba(230, 0, 0, 0.9);
    color: rgba(255, 255, 255, 0);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(230, 0, 0, 0.3);
}

.vertical-carousel-btn:hover {
    background: var(--primary-red);
    transform: scale(1.1);
    box-shadow: 0 6px 18px rgba(230, 0, 0, 0);
}

/* ===== OVERRIDE RAPIDI PER POSIZIONAMENTO ===== */
/* Decommenta e modifica questi valori per spostare rapidamente il carosello */

/*
.vertical-offers-carousel {
    --vertical-carousel-top: 100px;
    --vertical-carousel-right: 100px;
    --vertical-carousel-x: 0px;
    --vertical-carousel-y: 0px;
}
*/

/* ESEMPI PREIMPOSTATI - Decommenta quello che preferisci */

/* Posizione in alto a destra */
/*
.vertical-offers-carousel {
    --vertical-carousel-top: 20px;
    --vertical-carousel-right: 20px;
}
*/

/* Posizione centrata a destra */
/*
.vertical-offers-carousel {
    --vertical-carousel-top: 50%;
    --vertical-carousel-right: 20px;
    --vertical-carousel-y: -150px;
}
*/

/* Posizione in basso a destra */
/*
.vertical-offers-carousel {
    --vertical-carousel-top: auto;
    --vertical-carousel-bottom: 20px;
    --vertical-carousel-right: 20px;
}
*/

/* ===== PHILOSOPHY SECTION ===== */
.philosophy {
    padding: var(--section-padding);
    position: relative;
    overflow: hidden;
}

/* Philosophy overlays removed - using global body overlays */

/* ===== PRODUCTS CAROUSEL SECTION ===== */
.products-carousel {
    padding: var(--section-padding);
    position: relative;
    overflow: hidden;
}

.products-carousel .container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 3rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 60px rgba(255, 215, 0, 0.2);
    overflow: hidden;
    position: relative;
}

.products-carousel .container::before {
    content: '';
    position: absolute;
    top: -20%;
    left: -20%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%
    );
    animation: glassReflection 8s ease infinite;
    pointer-events: none;
}

.carousel-container {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    margin: 2rem 0;
}

.carousel-track {
    display: flex;
    gap: 2rem;
    padding: 1rem 0;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.carousel-track::-webkit-scrollbar {
    display: none;
}

.product-card {
    flex: 0 0 280px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transition: all 0.4s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    height: 380px;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(230, 0, 0, 0.2);
}

.product-image {
    width: 100%;
    height: 200px;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    text-align: center;
}

.product-name {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--light);
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.product-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    margin-bottom: 1rem;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    gap: 1rem;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--light);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.product-order-btn {
    background: var(--primary-red);
    color: var(--light);
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(230, 0, 0, 0.3);
}

.product-order-btn:hover {
    background: #cc0000;
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(230, 0, 0, 0.4);
}

.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: rgba(230, 0, 0, 0.9);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.carousel-btn:hover {
    background: var(--primary-red);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 8px 25px rgba(230, 0, 0, 0.4);
}

.carousel-btn-prev {
    left: 1rem;
}

.carousel-btn-next {
    right: 1rem;
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active,
.indicator:hover {
    background: var(--primary-red);
    transform: scale(1.2);
}

/* Using same animations as hero section */

.philosophy-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 3rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 60px rgba(255, 215, 0, 0.2);
    overflow: hidden;
}

.philosophy-content::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%
    );
    animation: glassReflection 8s ease infinite;
    pointer-events: none;
}

@keyframes glassReflection {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    50% {
        transform: translateX(0%) translateY(0%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

/* Philosophy label removed */

.philosophy-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    line-height: 1.2;
    margin-bottom: 3rem;
    color: #000000;
    margin-top: 1rem;
    text-shadow:
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 20px rgba(255, 215, 0, 0.6),
        0 0 30px rgba(230, 0, 0, 0.4),
        2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: projectGlow 3s ease-in-out infinite alternate;
}

.innovation-text {
    font-weight: 900;
}

.creativity-subtitle {
    display: block;
    font-size: clamp(1.2rem, 2.5vw, 1.8rem);
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 1rem;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.philosophy-cta {
    margin-top: 3rem;
}

.btn-philosophy {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    padding: 1.2rem 2.5rem;
    background: var(--primary-red);
    color: var(--light);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.4s ease;
    box-shadow: 0 8px 25px rgba(230, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-philosophy::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-philosophy:hover::before {
    left: 100%;
}

.btn-philosophy:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(230, 0, 0, 0.4);
    background: linear-gradient(45deg, #E60000, #FF2020);
}

.btn-arrow {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.btn-philosophy:hover .btn-arrow {
    transform: translateX(5px);
}

/* ===== MENU SECTION ===== */
.menu {
    padding: var(--section-padding);
}

/* ===== HORIZONTAL EXTRAS CAROUSEL ===== */
.extras-carousel {
    width: 100%;
    overflow: hidden;
    margin-bottom: 3rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.extras-track {
    display: flex;
    animation: scrollHorizontal 30s linear infinite;
    gap: 2rem;
    padding: 1rem 0;
}

.extra-text-item {
    flex-shrink: 0;
    min-width: 300px;
    padding: 1rem 1.5rem;
    text-align: center;
}

.extra-text-item h4 {
    color: var(--primary-gold);
    font-size: 1rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.extra-text-item p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.85rem;
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
}

.extra-text-item .price {
    color: #000000;
    font-size: 0.8rem;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    display: inline-block;
}

@keyframes scrollHorizontal {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Pause animation on hover */
.extras-carousel:hover .extras-track {
    animation-play-state: paused;
}

.menu .container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 3rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 60px rgba(255, 215, 0, 0.2);
    overflow: hidden;
    position: relative;
}

.menu .container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%
    );
    animation: glassReflection 8s ease infinite;
    pointer-events: none;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    color: var(--light);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.menu-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--gray-300);
    background: var(--light);
    color: var(--dark);
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    border-color: var(--primary-red);
    background: var(--primary-red);
    color: var(--light);
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.menu-item {
    background: rgba(26, 26, 26, 0.7);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.menu-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.menu-item h4 {
    color: var(--light);
    margin: 0;
}

.menu-item-category {
    background: var(--primary-gold);
    color: var(--dark);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.menu-item-description {
    color: var(--light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.menu-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-item-price {
    font-weight: 700;
    color: var(--light);
    font-size: 1.1rem;
}

/* Special items styling */
.menu-item.special h4 {
    color: var(--primary-red);
}

.menu-item.special .menu-item-description {
    color: var(--light);
}

.menu-item.special .menu-item-price {
    color: var(--light);
}

/* Maxi price styling */
.menu-item-price .maxi-price {
    color: #00ff00;
    font-weight: 700;
}

.menu-item-price:has(.maxi-price) {
    color: var(--light);
}

/* Alternative for browsers that don't support :has() */
.menu-item-price.has-maxi {
    color: var(--light);
}

.menu-item-price.has-maxi .maxi-price {
    color: #00ff00;
}

/* ===== CART STYLES ===== */
.cart-btn {
    position: relative;
    background: rgba(230, 0, 0, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-btn:hover {
    background: var(--primary-red);
    transform: scale(1.1);
}

.cart-icon {
    font-size: 1.5rem;
    color: white;
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--primary-gold);
    color: var(--dark);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
}

.cart-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.cart-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cart-header h2 {
    color: var(--light);
    margin: 0;
}

.cart-close {
    background: none;
    border: none;
    color: var(--light);
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.cart-close:hover {
    color: var(--primary-red);
}

.cart-body {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
}

/* Custom scrollbar for cart */
.cart-body::-webkit-scrollbar {
    width: 8px;
}

.cart-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.cart-body::-webkit-scrollbar-thumb {
    background: var(--primary-red);
    border-radius: 10px;
    transition: background 0.3s ease;
}

.cart-body::-webkit-scrollbar-thumb:hover {
    background: #cc0000;
}

/* Firefox scrollbar */
.cart-body {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-red) rgba(255, 255, 255, 0.1);
}

/* Smooth scrolling behavior */
.cart-body {
    scroll-behavior: smooth;
}

/* Enhanced cart items spacing for better scroll experience */
.cart-item {
    margin-bottom: 1rem;
    scroll-margin-top: 1rem;
}

.cart-item:last-child {
    margin-bottom: 0;
}

.cart-items {
    display: none;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    color: var(--light);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cart-item-size {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    background: var(--primary-red);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s ease;
}

.quantity-btn:hover {
    background: #cc0000;
}

.quantity-display {
    color: var(--light);
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.cart-item-price {
    color: var(--primary-gold);
    font-weight: 700;
    font-size: 1.1rem;
}

.cart-empty {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 2rem;
}

.cart-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 0 0 20px 20px;
}

.cart-total {
    text-align: center;
    margin-bottom: 1rem;
    color: var(--light);
    font-size: 1.2rem;
}

.cart-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Size Selection Modal */
.size-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2500;
    align-items: center;
    justify-content: center;
}

.size-content {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 400px;
    width: 90%;
}

.size-content h3 {
    color: var(--light);
    margin-bottom: 1.5rem;
}

.size-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.size-option {
    background: var(--primary-red);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.size-option:hover {
    background: #cc0000;
    transform: translateY(-2px);
}

/* Extras Selection Modal */
.extras-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2500;
    align-items: center;
    justify-content: center;
}

.extras-content {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.extras-content h3 {
    color: var(--light);
    margin-bottom: 1rem;
    text-align: center;
}

.base-price {
    color: var(--primary-gold);
    text-align: center;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.extras-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.extra-category h4 {
    color: var(--primary-red);
    margin-bottom: 0.8rem;
    font-size: 1rem;
    border-bottom: 1px solid rgba(230, 0, 0, 0.3);
    padding-bottom: 0.3rem;
}

.extra-checkbox,
.extra-radio {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.extra-checkbox:hover,
.extra-radio:hover {
    background: rgba(255, 255, 255, 0.05);
}

.extra-checkbox input,
.extra-radio input {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-red);
}

.extra-checkbox span,
.extra-radio span {
    color: var(--light);
    font-size: 0.9rem;
}

.extra-note-container {
    margin-top: 0.8rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.extra-note-container label {
    display: block;
    color: var(--primary-gold);
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.extra-note-input,
.extra-note-textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--light);
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
}

.extra-note-input:focus,
.extra-note-textarea:focus {
    outline: none;
    border-color: var(--primary-red);
    background: rgba(255, 255, 255, 0.08);
}

.extra-note-input::placeholder,
.extra-note-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.extra-note-textarea {
    min-height: 60px;
    max-height: 120px;
}

.extras-total {
    text-align: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    color: var(--primary-gold);
    font-size: 1.1rem;
}

.extras-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Cart Notification */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.cart-notification {
    animation: slideIn 0.3s ease;
}

/* Responsive Cart */
@media (max-width: 768px) {
    .cart-content {
        width: 95%;
        max-height: 90vh;
    }

    .cart-body {
        padding: 1rem;
        flex: 1;
        min-height: 0;
    }

    /* Mobile scrollbar */
    .cart-body::-webkit-scrollbar {
        width: 6px;
    }

    .cart-btn {
        width: 40px;
        height: 40px;
        margin-right: 0.5rem;
    }

    .cart-icon {
        font-size: 1.2rem;
    }

    .cart-count {
        width: 18px;
        height: 18px;
        font-size: 0.7rem;
    }

    .cart-item {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
        padding: 0.8rem;
    }

    .cart-item-controls {
        width: 100%;
        justify-content: space-between;
    }

    .cart-actions {
        flex-direction: column;
    }

    /* Extras Modal Responsive */
    .extras-content {
        padding: 1.5rem;
        max-height: 90vh;
    }

    .extras-grid {
        gap: 1rem;
    }

    .extra-category h4 {
        font-size: 0.9rem;
    }

    .extra-checkbox,
    .extra-radio {
        padding: 0.4rem;
        margin-bottom: 0.6rem;
    }

    .extra-checkbox span,
    .extra-radio span {
        font-size: 0.85rem;
    }

    .extras-actions {
        flex-direction: column;
    }

    .extra-note-container {
        padding: 0.8rem;
        margin-top: 0.6rem;
        margin-bottom: 0.8rem;
    }

    .extra-note-container label {
        font-size: 0.8rem;
        margin-bottom: 0.4rem;
    }

    .extra-note-input,
    .extra-note-textarea {
        padding: 0.6rem;
        font-size: 0.85rem;
    }

    .extra-note-textarea {
        min-height: 50px;
        max-height: 100px;
    }

    /* Extras Carousel Responsive */
    .extra-text-item {
        min-width: 250px;
        padding: 0.8rem 1rem;
    }

    .extra-text-item h4 {
        font-size: 0.9rem;
    }

    .extra-text-item p {
        font-size: 0.8rem;
    }

    .extra-text-item .price {
        font-size: 0.75rem;
        padding: 0.25rem 0.6rem;
        color: #000000;
        background: rgba(255, 255, 255, 0.9);
    }
}

.menu-item-order {
    padding: 0.4rem 0.8rem;
    background: var(--primary-red);
    color: var(--light);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    font-size: 0.8rem;
}

.menu-item-order:hover {
    background: #cc0000;
}

/* ===== OFFERS SECTION ===== */
.offers {
    padding: var(--section-padding);
    color: var(--light);
}

.offers .container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 3rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 60px rgba(255, 215, 0, 0.2);
    overflow: hidden;
    position: relative;
}

.offers .container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%
    );
    animation: glassReflection 8s ease infinite;
    pointer-events: none;
}

.offers-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.offer-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Override della classe highlight per le offer-card */
.offer-card.highlight,
.offer-card.highlight h3,
.offer-card.highlight p,
.offer-card.highlight .offer-icon {
    -webkit-text-fill-color: unset !important;
    background-clip: unset !important;
    -webkit-background-clip: unset !important;
    color: #ffffff !important;
    text-shadow: none !important;
}

.offer-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.offer-card h3 {
    color: #ffffff !important;
    margin-bottom: 1rem;
    font-weight: 600;
}

.offer-card p {
    color: rgba(255, 255, 255, 0.9) !important;
    margin: 0;
    line-height: 1.5;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--section-padding);
}

.contact .container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 3rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 60px rgba(255, 215, 0, 0.2);
    overflow: hidden;
    position: relative;
}

.contact .container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%
    );
    animation: glassReflection 8s ease infinite;
    pointer-events: none;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-icon {
    font-size: 1.5rem;
    width: 50px;
    height: 50px;
    background: var(--gray-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-item h4 {
    color: var(--light);
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.contact-item a {
    color: var(--primary-red);
    text-decoration: none;
    font-weight: 600;
}

.contact-item a:hover {
    text-decoration: underline;
}

.map-placeholder {
    background: var(--gray-100);
    border-radius: var(--border-radius);
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--gray-300);
}

.map-content {
    text-align: center;
}

.map-content h4 {
    color: var(--light);
    margin-bottom: 1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--dark);
    color: var(--light);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo-section {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.footer-logo {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    flex-shrink: 0;
}

.footer-brand-text {
    flex: 1;
}

.footer-brand h3 {
    color: var(--light);
    margin-bottom: 1rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.footer-links h4,
.footer-contact h4 {
    color: var(--light);
    margin-bottom: 1rem;
}

.footer-links ul {
    list-style: none;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-red);
}

.footer-contact p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 2rem;
    text-align: center;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0.5rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }

    .nav-logo {
        height: 40px;
        max-width: 150px;
    }

    .footer-logo-section {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        text-align: center;
    }

    .footer-logo {
        width: 50px;
        height: 50px;
    }
    
    .hero {
        min-height: 50vh;
    }

    .hero .container {
        margin: 0;
        padding: 1rem;
        border-radius: 0;
        max-width: 100%;
        width: 100%;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .hero-text {
        padding: 1rem 0.5rem;
        text-align: center;
        order: 2;
    }

    .hero-title {
        font-size: clamp(1.5rem, 4vw, 2.2rem);
        margin-bottom: 0.8rem;
    }

    .hero-description {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .hero-nav-buttons {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .hero-image {
        height: 30vh;
        order: 1;
        padding: 0.15rem;
    }

    .hero-image img {
        height: 25vh;
        width: 90%;
        max-width: 250px;
        /* Posiziona 5px a destra del carosello (60px + 100px + 5px = 165px) */
        transform: translate(100px, 60px);
    }

    .vertical-offers-carousel {
        width: 200px;
        height: 200px;
        margin-right: 1rem;
        /* Sposta 50px a destra in mobile per evitare taglio */
        transform: translate(calc(var(--vertical-carousel-x, 10px) + 80px), var(--vertical-carousel-y, -120px));
    }

    .vertical-carousel-controls {
        right: -50px;
    }

    .vertical-carousel-btn {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .offer-slide img {
        max-height: 140px;
    }

    .product-name-vertical {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-nav-buttons {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .philosophy-content {
        padding: 2rem 1rem;
        margin: 0 1rem;
        border-radius: 20px;
    }

    .philosophy-title {
        font-size: clamp(2rem, 4vw, 3rem);
    }

    .creativity-subtitle {
        font-size: clamp(1rem, 2vw, 1.4rem);
    }

    .btn-philosophy {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .products-carousel .container {
        padding: 2rem 1rem;
        border-radius: 20px;
    }

    .product-card {
        flex: 0 0 250px;
        padding: 1rem;
        height: 350px;
    }

    .product-image {
        height: 150px;
    }

    .product-name {
        font-size: 1rem;
    }

    .product-description {
        font-size: 0.8rem;
        min-height: 50px;
    }

    .product-footer {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }

    .product-price {
        font-size: 1.1rem;
        text-align: center;
    }

    .product-order-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
        width: 100%;
    }

    .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .carousel-btn-prev {
        left: 0.5rem;
    }

    .carousel-btn-next {
        right: 0.5rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .menu-filters {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    :root {
        --container-padding: 0 0.5rem;
    }
    
    .hero-buttons {
        width: 100%;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* ===== PRIVACY POPUP ===== */
.privacy-popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 3000;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.privacy-popup.show {
    display: flex;
}

.privacy-popup-content {
    background: rgba(26, 26, 26, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2.5rem;
    border: 2px solid var(--primary-red);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    animation: popupSlideIn 0.3s ease;
}

@keyframes popupSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.privacy-header {
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid var(--primary-red);
    padding-bottom: 1rem;
}

.privacy-header h3 {
    color: var(--light);
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
}

.privacy-body {
    margin-bottom: 2rem;
}

.privacy-body p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.6;
    text-align: justify;
}

.privacy-links {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
    justify-content: center;
}

.privacy-links a {
    color: var(--primary-gold);
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    border: 1px solid var(--primary-gold);
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
}

.privacy-links a:hover {
    background: var(--primary-gold);
    color: var(--dark);
    transform: translateY(-2px);
}

.privacy-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-accept {
    background: var(--primary-red);
    color: white;
    border: 2px solid var(--primary-red);
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 150px;
}

.btn-accept:hover {
    background: #cc0000;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(230, 0, 0, 0.4);
}

.btn-settings {
    background: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 150px;
}

.btn-settings:hover {
    background: var(--primary-gold);
    color: var(--dark);
    transform: translateY(-2px);
}

.btn-reject {
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 150px;
}

.btn-reject:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

/* Cookie Settings Modal */
.cookie-settings-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 3500;
    align-items: center;
    justify-content: center;
}

.cookie-settings-content {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.cookie-settings-content h3 {
    color: var(--light);
    margin-bottom: 1.5rem;
    text-align: center;
}

.cookie-categories {
    margin-bottom: 2rem;
}

.cookie-category {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.cookie-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.cookie-category h4 {
    color: var(--primary-gold);
    margin: 0;
    font-size: 1rem;
}

.cookie-category p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Cookie Switch */
.cookie-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.cookie-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-red);
}

input:disabled + .slider {
    background-color: #666;
    cursor: not-allowed;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.cookie-settings-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Privacy Popup Responsive */
@media (max-width: 768px) {
    .privacy-popup-content {
        padding: 2rem;
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    .privacy-header h3 {
        font-size: 1.2rem;
    }

    .privacy-body p {
        font-size: 0.9rem;
        text-align: left;
    }

    .privacy-links {
        flex-direction: column;
        gap: 0.8rem;
    }

    .privacy-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .btn-accept,
    .btn-settings,
    .btn-reject {
        width: 100%;
        padding: 1rem;
        font-size: 1rem;
        min-width: auto;
    }

    .cookie-settings-content {
        padding: 1.5rem;
        margin: 1rem;
    }

    .cookie-category-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .cookie-settings-actions {
        flex-direction: column;
    }

    .cookie-settings-actions button {
        width: 100%;
    }
}
