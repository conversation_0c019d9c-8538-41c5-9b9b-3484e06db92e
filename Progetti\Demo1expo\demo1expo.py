// App.js - Demo Pizza in Piazza
import React, { useState } from 'react';
import { View, Text, Button, FlatList, TouchableOpacity, TextInput, StyleSheet } from 'react-native';

const menu = [
  { id: '1', name: '<PERSON><PERSON><PERSON><PERSON>', price: 6 },
  { id: '2', name: '<PERSON><PERSON><PERSON>', price: 7 },
  { id: '3', name: '<PERSON><PERSON><PERSON><PERSON>', price: 8 },
];

const mockOrders = [
  { id: '101', items: ['Margheri<PERSON>', '<PERSON>av<PERSON>'], customer: '<PERSON>', address: 'Via Roma 10', status: 'In preparazione' },
  { id: '102', items: ['Cap<PERSON><PERSON><PERSON>'], customer: 'Giulia', address: 'Via Milano 22', status: 'Consegnato' },
];

export default function App() {
  const [view, setView] = useState('user');
  const [cart, setCart] = useState([]);
  const [orders, setOrders] = useState(mockOrders);

  const addToCart = (item) => {
    setCart([...cart, item]);
  };

  const confirmOrder = () => {
    const newOrder = {
      id: Date.now().toString(),
      items: cart.map(i => i.name),
      customer: 'Cliente Demo',
      address: 'Via Esempio 123',
      status: 'In preparazione'
    };
    setOrders([...orders, newOrder]);
    setCart([]);
    alert('Ordine confermato!');
  };

  const updateStatus = (id, newStatus) => {
    setOrders(orders.map(o => o.id === id ? { ...o, status: newStatus } : o));
  };

  if (view === 'admin') {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Dashboard Admin</Text>
        <FlatList
          data={orders}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <View style={styles.card}>
              <Text>Ordine #{item.id}</Text>
              <Text>Cliente: {item.customer}</Text>
              <Text>Indirizzo: {item.address}</Text>
              <Text>Prodotti: {item.items.join(', ')}</Text>
              <Text>Stato: {item.status}</Text>
              {item.status !== 'Consegnato' && (
                <Button title="Segna come consegnato" onPress={() => updateStatus(item.id, 'Consegnato')} />
              )}
            </View>
          )}
        />
        <Button title="Torna alla vista utente" onPress={() => setView('user')} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Pizza in Piazza</Text>
      <Text style={styles.subtitle}>Menù</Text>
      {menu.map((item) => (
        <TouchableOpacity key={item.id} style={styles.card} onPress={() => addToCart(item)}>
          <Text>{item.name} - {item.price}€</Text>
        </TouchableOpacity>
      ))}
      <Text style={styles.subtitle}>Carrello</Text>
      {cart.map((item, index) => (
        <Text key={index}>{item.name}</Text>
      ))}
      <Button title="Conferma Ordine" onPress={confirmOrder} disabled={cart.length === 0} />
      <Button title="Admin" onPress={() => setView('admin')} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    paddingTop: 50,
    flex: 1,
    backgroundColor: '#fff'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10
  },
  subtitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 10
  },
  card: {
    padding: 10,
    backgroundColor: '#f2f2f2',
    marginVertical: 5,
    borderRadius: 5
  }
});
