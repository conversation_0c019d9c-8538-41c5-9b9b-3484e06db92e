# Pizza in Piazza - Sito Web Ufficiale

## 🍕 Descrizione
Sito web ufficiale per "Pizza in Piazza" - pizzeria di Sarmato attiva dal 2009.
**"La pizza non è fast, è fast-astica!"**

## 🚀 Caratteristiche MVP Implementate

### ✅ Setup Progetto e Struttura Base
- [x] Inizializzazione repository Git
- [x] Setup struttura cartelle del progetto (src, assets, docs, etc.)
- [x] Configurazione ambiente di sviluppo
- [x] Setup package.json con dipendenze necessarie
- [x] Configurazione Tailwind CSS
- [x] Setup build system (Vite/HTTP-Server)

### ✅ Design System e Assets
- [x] Definizione palette colori (#000000, #FFFFFF, #E60000, #FFD700)
- [x] Selezione e integrazione font (Anton/Oswald per titoli, Roboto/Lato per testo)
- [x] Creazione logo e favicon ✅ (già completato)
- [x] Setup sistema di breakpoints responsive

### ✅ Struttura HTML Base
- [x] Creazione index.html con struttura semantica
- [x] Implementazione header con logo e navigazione
- [x] Struttura hero section con slogan
- [x] Layout sezione menù
- [x] Sezione offerte speciali
- [x] Sezione contatti e mappa
- [x] Footer completo

### ✅ Sistema Menù Dinamico
- [x] Integrazione file JSON menù nel progetto
- [x] Sviluppo parser JSON per categorie
- [x] Implementazione filtri per categorie (Pizze, Burger, Snack, etc.)
- [x] Rendering dinamico items menù
- [x] Gestione prezzi multipli (media/maxi, normale/double)
- [x] Implementazione sistema extras e opzioni speciali
- [x] Responsive design per visualizzazione menù mobile

### ✅ Funzionalità Interattive
- [x] Implementazione click-to-call per numeri telefono
- [x] Animazioni smooth scroll per navigazione
- [x] Effetti hover e transizioni CSS
- [x] Menu mobile responsive

## 🛠️ Tecnologie Utilizzate
- **HTML5** - Markup semantico
- **CSS3** - Styling avanzato
- **Tailwind CSS** - Framework CSS utility-first
- **JavaScript ES6+** - Interattività e logica
- **Vite** - Build tool e dev server
- **JSON** - Database menù dinamico

## 📱 Caratteristiche
- **Mobile-First Design** - Ottimizzato per smartphone
- **Responsive Layout** - Adattivo a tutti i dispositivi
- **Click-to-Call** - Numeri telefono cliccabili
- **Menù Dinamico** - Caricamento da JSON
- **Filtri Categoria** - Navigazione facile del menù
- **Animazioni Smooth** - Esperienza utente fluida
- **SEO Ottimizzato** - Meta tags e structured data

## 🚀 Come Avviare il Progetto

### Prerequisiti
- Node.js (versione 16 o superiore)
- npm o yarn

### Installazione
```bash
# Clona il repository
git clone [repository-url]

# Entra nella cartella del progetto
cd PiazzainPiazza

# Installa le dipendenze
npm install
```

### Sviluppo
```bash
# Avvia Tailwind CSS in watch mode
npx tailwindcss -i ./src/styles/main.css -o ./src/styles/output.css --watch

# In un altro terminale, avvia il server di sviluppo
npx http-server src -p 3000 -o
```

Il sito sarà disponibile su `http://localhost:3000`

### Build per Produzione
```bash
# Compila CSS per produzione
npx tailwindcss -i ./src/styles/main.css -o ./src/styles/output.css --minify

# Copia i file nella cartella dist
npm run build
```

## 📁 Struttura del Progetto
```
PiazzainPiazza/
├── src/
│   ├── index.html          # Pagina principale
│   ├── styles/
│   │   ├── main.css        # CSS sorgente con Tailwind
│   │   └── output.css      # CSS compilato
│   ├── js/
│   │   └── main.js         # JavaScript principale
│   └── data/
│       └── menu.json       # Database menù
├── Documenti/
│   ├── MVP.txt             # Specifiche progetto
│   └── Progetto_Step.md    # Piano di sviluppo
├── Logo.jpg                # Logo pizzeria
├── package.json            # Dipendenze npm
├── tailwind.config.js      # Configurazione Tailwind
└── README.md              # Questo file
```

## 🎨 Palette Colori
- **Nero**: #000000 (pizza-black)
- **Bianco**: #FFFFFF (pizza-white)  
- **Rosso**: #E60000 (pizza-red)
- **Oro**: #FFD700 (pizza-gold)

## 📞 Informazioni Pizzeria
- **Nome**: Pizza in Piazza
- **Dal**: 2009
- **Indirizzo**: Piazza Cortiglio, 5 - Sarmato (PC)
- **Telefoni**: ************ | ************
- **Orari**: Mar-Dom 11:00-14:30 | 17:30-22:00
- **Chiuso**: Lunedì e Domenica mattina

## 🚚 Servizio a Domicilio
- **Costo**: €2.00
- **GRATIS ogni Martedì**
- **Zone**: Sarmato, Castel San Giovanni, Borgonovo Val Tidone, Agazzino, Fontana Pradosa, Rottofreno

## 📋 Prossimi Sviluppi (SVP)
- [ ] Sistema ordinazione online demo
- [ ] Pagina admin ordini
- [ ] Galleria fotografica
- [ ] Integrazione Google Maps
- [ ] SEO locale avanzato
- [ ] Integrazione social media

## 👨‍💻 Sviluppato da
**AgTech Designe** - Soluzioni digitali professionali

---
*"La pizza non è fast, è fast-astica!"* 🍕
