{"categories": [{"name": "Le Pizze", "items": [{"name": "Affumicata", "ingredients": "pomodoro, fior di latte, scam<PERSON><PERSON>, speck", "prices": {"media": 8, "maxi": 17}}, {"name": "Autunnale", "ingredients": "fior di latte, crema di zucca, funghi, salsiccia", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "pomodor<PERSON>, fior di latte, mozzarella di bufala", "prices": {"media": 7, "maxi": 15}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, prosc., funghi, carcio<PERSON>, acci<PERSON>e, capperi, olive", "prices": {"media": 9, "maxi": 19}}, {"name": "Carbonara", "ingredients": "pomodoro, fior di latte, pancetta, uovo, grana", "prices": {"media": 9, "maxi": 19}}, {"name": "Crescenza e Coppa", "ingredients": "pomodoro, fior di latte, crescenza, coppa", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, crudo", "prices": {"media": 7, "maxi": 15}}, {"name": "Diavola Super", "ingredients": "pomodoro, fior di latte, salame piccante, zola", "prices": {"media": 8, "maxi": 17}}, {"name": "Focaccia Tipo <PERSON>", "ingredients": "fior di latte, crescenza", "prices": {"media": 7, "maxi": 15}}, {"name": "Friarielli e Salsiccia", "ingredients": "fior di latte, fria<PERSON><PERSON>, sals<PERSON><PERSON>", "prices": {"media": 8, "maxi": 17}}, {"name": "Frutti di Mare", "ingredients": "pomodoro, fior di latte, frutti di mare, olive, origano", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, funghi", "prices": {"media": 7, "maxi": 15}}, {"name": "Genovese", "ingredients": "fior di latte, pesto genovese", "prices": {"media": 6, "maxi": 13}}, {"name": "Mal<PERSON><PERSON>", "ingredients": "pomo<PERSON><PERSON>, fior di latte, wurs<PERSON>, sals<PERSON><PERSON>, salame piccante, p<PERSON>oni", "prices": {"media": 9, "maxi": 19}}, {"name": "Margh<PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, origano", "prices": {"media": 6, "maxi": 13}}, {"name": "<PERSON>ra", "ingredients": "pomodoro, aglio, origano, olio", "prices": {"media": 6, "maxi": 13}}, {"name": "Mediterranea", "ingredients": "pomodoro, fior di latte, acci<PERSON>e, olive, pomodorini e origano", "prices": {"media": 8, "maxi": 17}}, {"name": "Napoli", "ingredients": "pomodoro, fior di latte, acci<PERSON>e, or<PERSON>o", "prices": {"media": 7, "maxi": 15}}, {"name": "Pancetta e Scamorza", "ingredients": "fior di latte, pancetta, scamorza affumicata", "prices": {"media": 8, "maxi": 17}}, {"name": "Kebab", "ingredients": "pomodoro, fior di latte, carne kebab, patatine, cipolla, salsa yogurt e piccante", "prices": {"media": 9, "maxi": 19}}, {"name": "Pizza in Piazza", "ingredients": "pomodoro, fior di latte, wurstel, patatine, salsiccia", "prices": {"media": 8, "maxi": 17}}, {"name": "Porcini e Radicchio", "ingredients": "pomodoro, fior di latte, porcini, radicchio", "prices": {"media": 8, "maxi": 17}}, {"name": "Prosciutto", "ingredients": "pomodoro, fior di latte, prosciutto", "prices": {"media": 7, "maxi": 15}}, {"name": "<PERSON>uatt<PERSON>", "ingredients": "fior di latte, brie, zola, em<PERSON><PERSON>", "prices": {"media": 8, "maxi": 17}}, {"name": "Quattro <PERSON>", "ingredients": "pomodor<PERSON>, fior di latte, pros<PERSON><PERSON>o, fun<PERSON>i, carciofi", "prices": {"media": 9, "maxi": 19}}, {"name": "Rustica", "ingredients": "pomodoro, fior di latte, funghi, pancetta", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, salmone, panna", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, salsiccia", "prices": {"media": 7, "maxi": 15}}, {"name": "Tartufata", "ingredients": "crema di tartufo, fior di latte, porcini", "prices": {"media": 9, "maxi": 19}}, {"name": "Tirolese", "ingredients": "pomodoro, fior di latte, speck, zola", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, tonno, cipolle", "prices": {"media": 8, "maxi": 17}}, {"name": "Vegetariana", "ingredients": "pomodor<PERSON>, fior di latte, zucchine, melanzane e peperoni grigliati", "prices": {"media": 8, "maxi": 17}}]}, {"name": "Speciali", "items": [{"name": "Afrodisiaca", "ingredients": "salsa cocktail, fior di latte, gamberetti, rucola, olio piccante", "prices": {"media": 8, "maxi": 17}}, {"name": "Cheeseburger", "ingredients": "pomodoro, fior di latte, burger, cheddar, cipolla, salsa", "prices": {"media": 9, "maxi": 19}}, {"name": "Fattoria", "ingredients": "p<PERSON><PERSON><PERSON>, fior di latte, metà 4 formaggi, metà salumi assortiti", "prices": {"media": 9, "maxi": 19}}, {"name": "Light", "ingredients": "pomodoro, fior di latte, pomodor<PERSON>, zucchine, philadelphia, rucola", "prices": {"media": 9, "maxi": 19}}, {"name": "Logiciattina", "ingredients": "pomodoro, funghi champ<PERSON>on, sals<PERSON><PERSON>, pomodor<PERSON>, grana", "prices": {"media": 9, "maxi": 19}}, {"name": "Notaro alla Sarmatese", "ingredients": "pomodoro, fior di latte, salame p<PERSON>, rucola, grana, pomodori secchi", "prices": {"media": 9, "maxi": 19}}, {"name": "Pazza", "ingredients": "pomo<PERSON><PERSON>, fior di latte, cipolle, zola, p<PERSON><PERSON>, scamor<PERSON> affumicata", "prices": {"media": 9, "maxi": 19}}, {"name": "Valtellina", "ingredients": "pomodoro, fior di latte, bres<PERSON><PERSON>, rucola, scaglie di grana", "prices": {"media": 9, "maxi": 19}}]}, {"name": "Calzoni", "items": [{"name": "Classico", "ingredients": "pomodoro, fior di latte, prosciutto", "prices": {"media": 8, "maxi": 17}}, {"name": "Farcito", "ingredients": "pomo<PERSON><PERSON>, fior di latte, pros<PERSON><PERSON>o, fun<PERSON>i, carcio<PERSON>, acci<PERSON>e", "prices": {"media": 9, "maxi": 19}}, {"name": "Vegetarian<PERSON>", "ingredients": "pomo<PERSON><PERSON>, fior di latte, zucchine, melanzane, p<PERSON><PERSON> grigliati", "prices": {"media": 8, "maxi": 17}}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "nutella, (su richiesta fior di latte)", "prices": {"media": -1, "maxi": -1}, "note": "Prezzo non specificato"}]}, {"name": "Burgers", "items": [{"name": "Cheeseburger", "ingredients": "Pane fresco, burger di manzo, pomodori, insalata fresca, cipolla, cheddar e salsa burger", "prices": {"normale": 7.5, "double": 8.5}}, {"name": "Chickenburger", "ingredients": "Pane fresco, pollo croccante, lattuga fresca e maionese", "prices": {"normale": 7.5, "double": 8.5}}]}, {"name": "Kebab", "items": [{"name": "Kebab 'Made in Italy'", "ingredients": "carne di tacchino e vitello, pomodori, insalata, cipolle, salsa yogurt e piccante", "price": 7.0}]}, {"name": "Panini o Bataro", "items": [{"name": "Cotto, Zola", "price": 7.5}, {"name": "Pancetta, Zola", "price": 7.5}, {"name": "Coppa, Crescenza", "price": 7.5}, {"name": "Bresaola, Rucola, Grana", "price": 7.5}, {"name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "price": 7.5}, {"name": "Crudo, Rucola, Crescenza", "price": 7.5}, {"name": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "price": 7.5}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "items": [{"name": "Margh<PERSON><PERSON>", "ingredients": "pomodoro, fior di latte, origano", "price": 10.0}, {"name": "Farci<PERSON>", "ingredients": "a scelta tra l'elenco delle pizze", "price": 12.0, "note": "impasto alto"}]}, {"name": "Piatti", "items": [{"name": "<PERSON><PERSON><PERSON>", "ingredients": "pomodoro, fior di latte", "price": 3.99}, {"name": "Insalatona", "ingredients": "lattuga, pomodori, carote, mais, olive, parmigiano, pollo croccante", "price": 6.99}, {"name": "<PERSON><PERSON><PERSON>", "ingredients": "salumi e formaggi", "price": 9.99}]}, {"name": "Snack", "items": [{"name": "Mozza<PERSON>ne", "prices": {"6pz": 5.0}}, {"name": "Nuggets", "prices": {"6pz": 5.0}}, {"name": "Jalapenos", "prices": {"6pz": 5.0}}, {"name": "<PERSON><PERSON> Cipolla", "prices": {"6pz": 5.0}}, {"name": "<PERSON>", "prices": {"6pz": 5.0}}, {"name": "Rosticelle Mexico", "prices": {"6pz": 5.0}}, {"name": "Snack Mix", "prices": {"9pz": 9.99, "10pz": 9.99}}, {"name": "<PERSON><PERSON><PERSON>", "prices": {"piccole": 3.0, "medie": 4.0, "maxi": 5.0}}]}, {"name": "<PERSON><PERSON><PERSON>", "items": [{"name": "Profiteroles", "price": 4.5}, {"name": "Tiramisù", "price": 4.5}, {"name": "ecc...", "note": "chiedere per info"}]}, {"name": "Bibite", "items": [{"name": "Acqua 50cl", "price": 1.0}, {"name": "Lattina 33cl", "price": 2.5}, {"name": "Bottiglia 50cl", "price": 3.5}, {"name": "Bottiglia 1,5L", "price": 4.5}, {"name": "Estathè Brick", "price": 1.2}, {"name": "Redbull 33cl", "price": 3.5}, {"name": "Birra 33cl", "price": 3.5}, {"name": "Birra 66cl", "price": 4.5}]}], "extras": {"title": "Extra", "items": [{"name": "Verdure", "price": 0.5}, {"name": "<PERSON><PERSON><PERSON>", "price": 1.5}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "price": 1.5}, {"name": "<PERSON><PERSON><PERSON> napoli", "price": 1.5}, {"name": "<PERSON><PERSON>", "price": 3.0}, {"name": "<PERSON><PERSON>", "price": 3.0}]}, "special_options": {"pizza_formats": [{"name": "Formato Baby", "note": "meno 2€ dalla media"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "note": "a soli 1€ in più"}], "dough_types": [{"name": "<PERSON><PERSON> Inte<PERSON> o Ka<PERSON>", "price_increase": 2.0, "note": "+2€ / +3€ maxi"}]}, "info": {"name": "Pizza In Piazza", "address": "<PERSON><PERSON>, 5, <PERSON><PERSON><PERSON>", "phone_numbers": ["346 / 811 . 6721", "338 / 765 . 5879"], "opening_hours": {"weekdays": "Martedì - Domenica", "hours": "11:00-14:30 | 17:30-22:00", "closing_days": "Lunedì e Domenica solo mattina"}, "delivery": {"cost": 2.0, "free_day": "Ogni Martedì il servizio a domicilio è GRATIS", "areas": "<PERSON><PERSON><PERSON>, Castel San Giovanni, Borgonovo Val Tidone, <PERSON><PERSON><PERSON><PERSON>, Fontana P<PERSON>sa, Rottofreno"}}}